// 国际化分类配置文件
// 支持多语言的AI工具分类配置

import { useTranslations } from 'next-intl';
import { getTranslations } from 'next-intl/server';

export interface CategoryConfig {
  slug: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export interface CategoryOption {
  value: string;
  label: string;
}

// 分类的基础配置（不包含翻译文本）
export const CATEGORY_BASE_CONFIGS = [
  {
    slug: 'productivity',
    icon: '⚡',
    color: '#14B8A6'
  },
  {
    slug: 'education',
    icon: '📚',
    color: '#A855F7'
  },
  {
    slug: 'text-generation',
    icon: '📝',
    color: '#3B82F6'
  },
  {
    slug: 'image-generation',
    icon: '🎨',
    color: '#10B981'
  },
  {
    slug: 'code-generation',
    icon: '💻',
    color: '#8B5CF6'
  },
  {
    slug: 'audio-processing',
    icon: '🎵',
    color: '#EF4444'
  },
  {
    slug: 'video-editing',
    icon: '🎬',
    color: '#06B6D4'
  },
  {
    slug: 'translation',
    icon: '🌐',
    color: '#84CC16'
  },
  {
    slug: 'search-engines',
    icon: '🔍',
    color: '#F97316'
  },
  {
    slug: 'marketing',
    icon: '📈',
    color: '#EC4899'
  },
  {
    slug: 'customer-service',
    icon: '🎧',
    color: '#F59E0B'
  },
  {
    slug: 'data-analysis',
    icon: '📊',
    color: '#F59E0B'
  }
];

// 客户端钩子：获取国际化的分类配置
export function useCategoryConfigs(): CategoryConfig[] {
  const t = useTranslations('categories');
  
  return CATEGORY_BASE_CONFIGS.map(config => ({
    slug: config.slug,
    name: t(`category_names.${config.slug}`),
    description: t(`category_descriptions.${config.slug}`),
    icon: config.icon,
    color: config.color
  }));
}

// 客户端钩子：获取分类选项（用于下拉框等）
export function useCategoryOptions(): CategoryOption[] {
  const configs = useCategoryConfigs();
  return configs.map(config => ({
    value: config.slug,
    label: config.name
  }));
}

// 客户端钩子：获取包含"所有分类"选项的分类选项
export function useCategoryOptionsWithAll(): CategoryOption[] {
  const t = useTranslations('categories');
  const options = useCategoryOptions();
  
  return [
    { value: '', label: t('all_categories') },
    ...options
  ];
}

// 客户端钩子：获取分类名称
export function useCategoryName(slug: string): string {
  const t = useTranslations('categories');
  return t(`category_names.${slug}`) || slug;
}

// 客户端钩子：获取分类描述
export function useCategoryDescription(slug: string): string {
  const t = useTranslations('categories');
  return t(`category_descriptions.${slug}`) || '';
}

// 服务器端函数：获取国际化的分类配置
export async function getCategoryConfigs(locale?: string): Promise<CategoryConfig[]> {
  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });
  
  return CATEGORY_BASE_CONFIGS.map(config => ({
    slug: config.slug,
    name: t(`category_names.${config.slug}`),
    description: t(`category_descriptions.${config.slug}`),
    icon: config.icon,
    color: config.color
  }));
}

// 服务器端函数：获取分类选项
export async function getCategoryOptions(locale?: string): Promise<CategoryOption[]> {
  const configs = await getCategoryConfigs(locale);
  return configs.map(config => ({
    value: config.slug,
    label: config.name
  }));
}

// 服务器端函数：获取包含"所有分类"选项的分类选项
export async function getCategoryOptionsWithAll(locale?: string): Promise<CategoryOption[]> {
  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });
  const options = await getCategoryOptions(locale);
  
  return [
    { value: '', label: t('all_categories') },
    ...options
  ];
}

// 服务器端函数：获取分类名称
export async function getCategoryName(slug: string, locale?: string): Promise<string> {
  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });
  return t(`category_names.${slug}`) || slug;
}

// 服务器端函数：获取分类描述
export async function getCategoryDescription(slug: string, locale?: string): Promise<string> {
  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });
  return t(`category_descriptions.${slug}`) || '';
}

// 服务器端函数：获取分类配置
export async function getCategoryConfig(slug: string, locale?: string): Promise<CategoryConfig | undefined> {
  const configs = await getCategoryConfigs(locale);
  return configs.find(config => config.slug === slug);
}

// 验证分类是否存在的辅助函数
export function isValidCategory(slug: string): boolean {
  return CATEGORY_BASE_CONFIGS.some(config => config.slug === slug);
}

// 获取所有分类slug的数组
export const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map(config => config.slug);

// 分类元数据映射（slug -> 基础配置）
export const CATEGORY_BASE_METADATA: Record<string, typeof CATEGORY_BASE_CONFIGS[0]> = 
  CATEGORY_BASE_CONFIGS.reduce((acc, config) => {
    acc[config.slug] = config;
    return acc;
  }, {} as Record<string, typeof CATEGORY_BASE_CONFIGS[0]>);
