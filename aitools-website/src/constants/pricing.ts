/**
 * 统一的价格配置文件
 * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中
 */

// 基础价格配置
export const PRICING_CONFIG = {
  // 优先发布服务价格
  PRIORITY_LAUNCH: {
    // 显示价格（元）
    displayPrice: 19.9,
    // 原价（元）- 用于显示打折前价格
    originalPrice: 49.9,
    // Stripe价格（分为单位）
    stripeAmount: 1990,
    // 原价Stripe金额（分为单位）
    originalStripeAmount: 4990,
    // 货币
    currency: 'USD',
    // Stripe货币代码（小写）
    stripeCurrency: 'usd', // 注意：当前使用USD进行测试
    // 产品名称
    productName: 'AI工具优先发布服务',
    // 产品描述
    description: '让您的AI工具获得优先审核和推荐位置',
    // 限时优惠信息
    promotion: {
      // 是否启用限时优惠
      enabled: true,
      // 优惠描述
      description: '限时优惠 - 前100个付费用户',
      // 折扣百分比
      discountPercent: 50,
      // 剩余名额（这个可以从数据库动态获取）
      remainingSlots: 85
    },
    // 功能特性
    features: [
      '可选择任意发布日期',
      '优先审核处理',
      '首页推荐位置',
      '专属客服支持'
    ]
  },
  
  // 免费发布配置
  FREE_LAUNCH: {
    displayPrice: 0,
    stripeAmount: 0,
    currency: 'USD',
    stripeCurrency: 'usd',
    productName: '免费发布服务',
    description: '选择一个月后的任意发布日期',
    features: [
      '免费提交审核',
      '发布日期：一个月后起',
      '正常审核流程',
      '标准展示位置'
    ]
  }
} as const;

// 发布选项配置
export const LAUNCH_OPTIONS = [
  {
    id: 'free' as const,
    title: '免费发布',
    description: PRICING_CONFIG.FREE_LAUNCH.description,
    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,
    features: PRICING_CONFIG.FREE_LAUNCH.features
  },
  {
    id: 'paid' as const,
    title: '优先发布',
    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,
    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,
    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,
    recommended: true
  }
] as const;

// 工具定价类型配置
export const TOOL_PRICING_TYPES = {
  FREE: {
    value: 'free',
    label: '免费',
    color: 'bg-green-100 text-green-800'
  },
  FREEMIUM: {
    value: 'freemium',
    label: '免费增值',
    color: 'bg-blue-100 text-blue-800'
  },
  PAID: {
    value: 'paid',
    label: '付费',
    color: 'bg-orange-100 text-orange-800'
  }
} as const;

// 工具定价选项（用于筛选）
export const TOOL_PRICING_OPTIONS = [
  { value: '', label: '所有价格' },
  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },
  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },
  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }
] as const;

// 工具定价选项（用于表单）
export const TOOL_PRICING_FORM_OPTIONS = [
  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },
  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },
  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }
] as const;

// 类型定义
export type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];
export type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];

// 辅助函数
export const getPricingConfig = (optionId: LaunchOptionId) => {
  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;
};

export const getToolPricingColor = (pricing: string) => {
  switch (pricing) {
    case TOOL_PRICING_TYPES.FREE.value:
      return TOOL_PRICING_TYPES.FREE.color;
    case TOOL_PRICING_TYPES.FREEMIUM.value:
      return TOOL_PRICING_TYPES.FREEMIUM.color;
    case TOOL_PRICING_TYPES.PAID.value:
      return TOOL_PRICING_TYPES.PAID.color;
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getToolPricingText = (pricing: string) => {
  switch (pricing) {
    case TOOL_PRICING_TYPES.FREE.value:
      return TOOL_PRICING_TYPES.FREE.label;
    case TOOL_PRICING_TYPES.FREEMIUM.value:
      return TOOL_PRICING_TYPES.FREEMIUM.label;
    case TOOL_PRICING_TYPES.PAID.value:
      return TOOL_PRICING_TYPES.PAID.label;
    default:
      return pricing;
  }
};

// 格式化价格显示
export const formatPrice = (price: number, locale?: string) => {
  if (price === 0) {
    return locale === 'zh' ? '免费' : 'Free';
  }
  return `¥${price}`;
};

// 格式化原价显示（带删除线）
export const formatOriginalPrice = (price: number, locale?: string) => {
  if (price === 0) {
    return locale === 'zh' ? '免费' : 'Free';
  }
  return `¥${price}`;
};

// 获取促销信息
export const getPromotionInfo = () => {
  return PRICING_CONFIG.PRIORITY_LAUNCH.promotion;
};

// 检查是否有促销活动
export const hasActivePromotion = () => {
  const promotion = PRICING_CONFIG.PRIORITY_LAUNCH.promotion;
  return promotion.enabled && promotion.remainingSlots > 0;
};

// 格式化Stripe金额显示
export const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: currency.toUpperCase(),
    minimumFractionDigits: 2,
  }).format(amount / 100);
};
