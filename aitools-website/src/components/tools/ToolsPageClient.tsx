'use client';

import React, { useState } from 'react';
import { usePathname } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import ToolCard from '@/components/ToolCard';
import ErrorMessage from '@/components/ErrorMessage';
import { Tool } from '@/lib/api';
import { TOOL_PRICING_OPTIONS } from '@/constants/pricing';
import { useCategoryOptionsWithAll } from '@/constants/categories-i18n';
import { Search, Filter, Grid, List, ChevronDown } from 'lucide-react';
import { Locale } from '@/i18n/config';
import ToolCardClient from '../ToolCardClient';

// 使用统一的价格选项配置
const pricingOptions = TOOL_PRICING_OPTIONS;

// Sort options will be defined inside component to use translations

interface ToolsPageClientProps {
  initialTools: Tool[];
  error: string | null;
}

export default function ToolsPageClient({ initialTools, error }: ToolsPageClientProps) {
  const [tools] = useState<Tool[]>(initialTools);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedPricing, setSelectedPricing] = useState('');
  const [sortBy, setSortBy] = useState('popular');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  const pathname = usePathname();
  const t = useTranslations('tools_page');

  // 使用国际化的分类选项
  const categories = useCategoryOptionsWithAll();

  // Extract current locale from pathname
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  // Define sort options with translations
  const sortOptions = [
    { value: 'popular', label: t('sort_popular') },
    { value: 'newest', label: t('sort_newest') },
    { value: 'name', label: t('sort_name') },
    { value: 'views', label: t('sort_views') }
  ];

  // Filter and sort tools
  const filteredTools = tools.filter((tool: Tool) => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = !selectedCategory || tool.category === selectedCategory;
    const matchesPricing = !selectedPricing || tool.pricing === selectedPricing;

    return matchesSearch && matchesCategory && matchesPricing;
  });

  const sortedTools = [...filteredTools].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return (b.likes || 0) - (a.likes || 0);
      case 'views':
        return (b.views || 0) - (a.views || 0);
      case 'name':
        return a.name.localeCompare(b.name);
      case 'newest':
        if (a.launchDate && b.launchDate) {
          return new Date(b.launchDate).getTime() - new Date(a.launchDate).getTime();
        }
        return new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime();
      default:
        return 0;
    }
  });

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ErrorMessage message={error} />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">{t('title')}</h1>
        <p className="text-lg text-gray-600">
          {t('subtitle', { count: tools.length })}
        </p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        {/* Search Bar */}
        <div className="relative mb-4">
          <input
            type="text"
            placeholder={t('search_placeholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
        </div>

        {/* Filter Toggle Button (Mobile) */}
        <div className="md:hidden mb-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
          >
            <Filter className="mr-2 h-4 w-4" />
            {t('filter_options')}
            <ChevronDown className={`ml-2 h-4 w-4 transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Filters */}
        <div className={`grid grid-cols-1 md:grid-cols-4 gap-4 ${showFilters ? 'block' : 'hidden md:grid'}`}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('category')}</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {categories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('pricing')}</label>
            <select
              value={selectedPricing}
              onChange={(e) => setSelectedPricing(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {pricingOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('sort')}</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">{t('view')}</label>
            <div className="flex rounded-lg border border-gray-300">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Grid className="h-4 w-4 mx-auto" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <List className="h-4 w-4 mx-auto" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="mb-6">
        <p className="text-gray-600">
          {t('results_count', { count: sortedTools.length })}
          {searchTerm && ` ${t('search_for', { term: searchTerm })}`}
          {selectedCategory && ` ${t('in_category', { category: categories.find(c => c.value === selectedCategory)?.label || '' })}`}
        </p>
      </div>

      {/* Tools Grid/List */}
      {sortedTools.length > 0 ? (
        <div className={viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
          : 'space-y-4'
        }>
          {sortedTools.map((tool) => (
            <ToolCardClient key={tool._id} tool={tool} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('no_results_title')}</h3>
          <p className="text-gray-600">
            {t('no_results_desc')}
          </p>
        </div>
      )}

      {/* Pagination would go here in a real application */}
    </div>
  );
}
