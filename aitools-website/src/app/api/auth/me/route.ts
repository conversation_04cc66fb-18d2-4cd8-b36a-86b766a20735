import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../../lib/auth';
import dbConnect from '../../../../lib/mongodb';
import User from '../../../../models/User';

// GET /api/auth/me - 获取当前用户信息
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      );
    }
    
    await dbConnect();
    
    const user = await User.findOne({ email: session.user.email })
      .select('-emailVerificationToken -emailVerificationExpires');
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        bio: user.bio,
        website: user.website,
        location: user.location,
        role: user.role,
        emailVerified: user.emailVerified,
        isActive: user.isActive,
        accounts: user.accounts.map((acc: any) => ({
          provider: acc.provider,
          providerId: acc.providerId,
        })),
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
      }
    });
    
  } catch (error) {
    console.error('Get user info error:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误，请稍后重试' },
      { status: 500 }
    );
  }
}

// PUT /api/auth/me - 更新用户信息
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: '未登录' },
        { status: 401 }
      );
    }
    
    await dbConnect();
    
    const { name, avatar, bio, website, location } = await request.json();

    // 验证输入
    if (name && (typeof name !== 'string' || name.trim().length === 0 || name.length > 100)) {
      return NextResponse.json(
        { success: false, error: '用户名格式不正确' },
        { status: 400 }
      );
    }

    if (avatar && typeof avatar !== 'string') {
      return NextResponse.json(
        { success: false, error: '头像URL格式不正确' },
        { status: 400 }
      );
    }

    if (bio && (typeof bio !== 'string' || bio.length > 500)) {
      return NextResponse.json(
        { success: false, error: '个人简介不能超过500个字符' },
        { status: 400 }
      );
    }

    if (website && (typeof website !== 'string' || (website.trim() && !/^https?:\/\/.+/.test(website.trim())))) {
      return NextResponse.json(
        { success: false, error: '网站地址格式不正确' },
        { status: 400 }
      );
    }

    if (location && (typeof location !== 'string' || location.length > 100)) {
      return NextResponse.json(
        { success: false, error: '所在地不能超过100个字符' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (avatar !== undefined) updateData.avatar = avatar;
    if (bio !== undefined) updateData.bio = bio.trim();
    if (website !== undefined) updateData.website = website.trim();
    if (location !== undefined) updateData.location = location.trim();
    
    const user = await User.findOneAndUpdate(
      { email: session.user.email },
      updateData,
      { new: true, runValidators: true }
    ).select('-emailVerificationToken -emailVerificationExpires');
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: '用户信息更新成功',
      data: {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        bio: user.bio,
        website: user.website,
        location: user.location,
        role: user.role,
        emailVerified: user.emailVerified,
      }
    });
    
  } catch (error) {
    console.error('Update user info error:', error);
    return NextResponse.json(
      { success: false, error: '服务器错误，请稍后重试' },
      { status: 500 }
    );
  }
}
