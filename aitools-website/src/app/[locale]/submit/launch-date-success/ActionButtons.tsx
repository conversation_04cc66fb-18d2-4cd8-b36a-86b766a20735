import { getTranslations } from 'next-intl/server';
import { <PERSON> } from '@/i18n/routing';

interface ActionButtonsProps {
  locale: string;
}

export default async function ActionButtons({ locale }: ActionButtonsProps) {
  const t = await getTranslations('submit.success');

  return (
    <div className="flex flex-col sm:flex-row gap-4 justify-center">
      <Link
        href="/profile/submitted"
        className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-blue-700 transition-colors"
      >
        {t('view_submissions')}
      </Link>
      <Link
        href="/"
        className="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-gray-700 transition-colors"
      >
        {t('back_to_home')}
      </Link>
    </div>
  );
}
