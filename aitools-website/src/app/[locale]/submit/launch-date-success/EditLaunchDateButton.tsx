import { getTranslations } from 'next-intl/server';
import { Link } from '@/i18n/routing';

interface EditLaunchDateButtonProps {
  toolId: string;
  toolStatus: string;
  launchOption: string;
}

export default async function EditLaunchDateButton({ toolId, toolStatus, launchOption }: EditLaunchDateButtonProps) {
  const t = await getTranslations('submit.success');

  // 只有在 pending 或 approved 状态下才显示按钮
  if (!['pending', 'approved', 'draft'].includes(toolStatus)) {
    return null;
  }

  return (
    <Link
      href={`/submit/launch-date/${toolId}?mode=edit`}
      className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
    >
      {launchOption ==='free'? t('upgrade_to_paid') :  t('edit_launch_date')}
    </Link>
  );
}
