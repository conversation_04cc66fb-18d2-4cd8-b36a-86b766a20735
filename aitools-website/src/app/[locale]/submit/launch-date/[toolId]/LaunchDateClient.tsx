'use client';

import { useState } from 'react';
import { useRouter } from '@/i18n/routing';
import { useLocale, useTranslations } from 'next-intl';
import LaunchDateSelector from '@/components/LaunchDateSelector';

interface LaunchDateClientProps {
  toolId: string;
  locale: string;
  currentOption?: 'free' | 'paid';
  currentDate?: string;
  minFreeDate?: string;
  minPaidDate?: string;
  hasPaidOrder?: boolean;
  orderId?: string;
  isEditMode?: boolean;
}

export default function LaunchDateClient({
  toolId,
  locale,
  currentOption = 'free',
  currentDate,
  minFreeDate,
  minPaidDate,
  hasPaidOrder = false,
  orderId,
  isEditMode = false
}: LaunchDateClientProps) {
  const router = useRouter();
  const t = useTranslations('launch');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (option: 'free' | 'paid', selectedDate: string) => {
    setIsSubmitting(true);
    setError('');

    try {
      // 创建模式的逻辑（原有逻辑）
      const response = await fetch(`/api/tools/${toolId}/launch-date`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Locale': locale,
        },
        body: JSON.stringify({
          launchOption: option,
          selectedDate,
          hasPaidOrder
        }),
      });

      const data = await response.json();

      if (data.success) {
        if (option === 'paid' && data.data.paymentUrl) {
          // 跳转到支付页面
          window.location.href = data.data.paymentUrl;
        } else {
          // 免费选项，直接进入审核
          router.push(`/submit/launch-date-success?toolId=${toolId}`);
        }
      } else {
        setError(data.message || t('submit_failed'));
      }
    } catch {
      setError(t('network_error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <LaunchDateSelector
      toolId={toolId}
      currentOption={hasPaidOrder ? 'paid' : currentOption}
      currentDate={currentDate}
      isEditing={isEditMode}
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
      error={error}
      hasPaidOrder={hasPaidOrder}
    />
  );
}
