import { redirect } from '@/i18n/routing';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import mongoose from 'mongoose';
import { CheckCircle, Calendar, CreditCard, ArrowRight } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import { Locale } from '@/i18n/config';
import { Link } from '@/i18n/routing';

interface PageProps {
  params: Promise<{
    locale: Locale;
  }>;
  searchParams: Promise<{
    toolId?: string;
  }>;
}

async function getToolData(toolId: string, userEmail: string) {
  try {
    await dbConnect();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(toolId)) {
      return null;
    }

    // 查找用户
    const user = await User.findOne({ email: userEmail });
    if (!user) {
      return null;
    }

    // 查找工具并验证所有权
    const tool = await Tool.findOne({
      _id: toolId,
      submittedBy: user._id
    }).lean() as any;

    if (!tool) {
      return null;
    }

    return {
      ...tool,
      _id: tool._id.toString(),
      submittedBy: tool.submittedBy.toString(),
      createdAt: tool.createdAt?.toISOString(),
      updatedAt: tool.updatedAt?.toISOString(),
    };
  } catch (error) {
    console.error('Error fetching tool data:', error);
    return null;
  }
}

export default async function ToolInfoSuccessPage({ params, searchParams }: PageProps) {
  const session = await getServerSession(authOptions);
  const { locale } = await params;
  const { toolId } = await searchParams;

  // 获取翻译
  const t = await getTranslations('submit.success');

  // 检查用户是否已登录
  if (!session?.user?.email) {
    redirect({ href: '/', locale });
  }

  // 检查是否有工具ID
  if (!toolId) {
    redirect({ href: '/', locale });
  }

  // 获取工具数据
  const tool = await getToolData(toolId!, session!.user!.email!);

  if (!tool) {
    redirect({ href: '/', locale });
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Success Header */}
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <div className="bg-green-100 rounded-full p-3">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('tool_info_submitted_title')}
        </h1>

        <p className="text-lg text-gray-600">
          {t('tool_info_submitted_desc')}
        </p>
      </div>

      {/* Tool Information Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('tool_info_title')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">{t('tool_name_label')}</span>
            <span className="ml-2 text-gray-900">{tool.name}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">{t('current_status_label')}</span>
            <span className="ml-2 text-gray-900">{t('status_draft')}</span>
          </div>
        </div>
      </div>

      {/* Next Step Guide */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <div className="flex items-center mb-4">
          <Calendar className="h-6 w-6 text-blue-600 mr-2" />
          <h3 className="text-lg font-semibold text-blue-900">
            {t('next_step_title')}
          </h3>
        </div>
        
        <p className="text-blue-800 mb-6">
          {t('next_step_desc')}
        </p>

        {/* Action Button */}
        <div className="flex justify-center">
          <Link
            href={`/submit/launch-date/${tool._id}`}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium text-center hover:bg-blue-700 transition-colors flex items-center"
          >
            {t('set_launch_date_button')}
            <ArrowRight className="h-5 w-5 ml-2" />
          </Link>
        </div>
      </div>

      {/* Alternative Actions */}
      {/* <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Link
          href="/profile/submitted"
          className="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-gray-700 transition-colors"
        >
          {t('view_submissions')}
        </Link>
        <Link
          href="/"
          className="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium text-center hover:bg-gray-200 transition-colors border border-gray-300"
        >
          {t('back_to_home')}
        </Link>
      </div> */}
    </div>
  );
}
