import React from 'react';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import ToolsPageClient from '@/components/tools/ToolsPageClient';
import { apiClient } from '@/lib/api';
import { getToolListStructuredData, getBreadcrumbStructuredData } from '@/lib/seo/structuredData';
import { type Locale } from '@/i18n/config';

// 强制动态渲染，避免构建时的 API 调用问题
export const dynamic = 'force-dynamic';

interface Props {
  params: Promise<{ locale: Locale }>;
}

// 生成动态metadata
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'tools' });

  try {
    // 获取工具总数用于动态描述
    const response = await apiClient.getTools({
      status: 'published',
      limit: 1
    });

    const totalTools = response.success && response.data ? response.data.pagination.totalItems : 0;

    const title = t('page_title');
    const description = t('page_description', { count: totalTools });
    const keywords = t('page_keywords');

    return {
      title,
      description,
      keywords,
      authors: [{ name: locale === 'zh' ? 'AI工具导航团队' : 'AI Tools Directory Team' }],
      robots: {
        index: true,
        follow: true,
      },
      openGraph: {
        type: 'website',
        locale: locale === 'zh' ? 'zh_CN' : 'en_US',
        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/${locale}/tools`,
        siteName: t('site_name'),
        title,
        description,
        images: [
          {
            url: '/og-tools.jpg',
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: ['/og-tools.jpg'],
      },
      alternates: {
        canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/${locale}/tools`,
      },
    };
  } catch (error) {
    // 如果获取数据失败，返回默认metadata
    const fallbackTitle = locale === 'zh' ? 'AI工具目录 - 发现最好的人工智能工具' : 'AI Tools Directory - Discover the Best AI Tools';
    const fallbackDescription = locale === 'zh'
      ? '浏览完整的AI工具目录，发现适合您需求的人工智能工具。包含文本生成、图像创作、数据分析、自动化等各类AI工具。'
      : 'Browse the complete AI tools directory and discover artificial intelligence tools that suit your needs. Including text generation, image creation, data analysis, automation and other AI tools.';
    const fallbackKeywords = locale === 'zh'
      ? 'AI工具目录,人工智能工具,AI工具列表,机器学习工具,深度学习工具,AI应用,自动化工具,智能工具'
      : 'AI tools directory,artificial intelligence tools,AI tools list,machine learning tools,deep learning tools,AI applications,automation tools,smart tools';

    return {
      title: fallbackTitle,
      description: fallbackDescription,
      keywords: fallbackKeywords,
    };
  }
}

// 服务端数据获取函数
async function getToolsData() {
  try {
    const response = await apiClient.getTools({
      status: 'published',
      limit: 100
    });

    if (response.success && response.data) {
      return {
        tools: response.data.tools,
        error: null
      };
    } else {
      return {
        tools: [],
        error: response.error || 'Failed to fetch tools list'
      };
    }
  } catch (error) {
    console.error('Failed to fetch tools:', error);
    return {
      tools: [],
      error: 'Failed to fetch tools list, please try again later'
    };
  }
}

export default async function ToolsPage({ params }: Props) {
  const { locale } = await params;
  const { tools, error } = await getToolsData();
  const t = await getTranslations({ locale, namespace: 'tools' });

  // 生成结构化数据
  const toolListStructuredData = tools.length > 0 ? getToolListStructuredData(tools) : null;
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: t('breadcrumb_home'), url: `/` },
    { name: t('breadcrumb_tools'), url: `/tools` }
  ]);

  return (
    <>
      {/* 结构化数据 */}
      {toolListStructuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(toolListStructuredData)
          }}
        />
      )}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label={t('breadcrumb_aria_label')}>
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                {t('breadcrumb_home')}
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">{t('breadcrumb_tools')}</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <ToolsPageClient initialTools={tools} error={error} />
    </>
  );
}
