'use client';

import { useState, useEffect } from 'react';
import { useRouter } from '@/i18n/routing';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import StripeCheckoutForm from '@/components/StripeCheckoutForm';
import { formatStripeAmount } from '@/constants/pricing';
import { CreditCard, Shield, CheckCircle, AlertCircle } from 'lucide-react';
import { apiClient } from '@/lib/api';
import { useTranslations } from 'next-intl';

// 初始化Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface OrderData {
  _id: string;
  type: string;
  amount: number;
  currency: string;
  status: string;
  description: string;
  selectedLaunchDate: string | null;
  createdAt: string | null;
  paidAt: string | null;
  tool: {
    _id: string;
    name: string;
    description: string;
  } | null;
  toolId: string | null;
}

interface CheckoutClientProps {
  order: OrderData;
  orderId: string;
}

export default function CheckoutClient({ order, orderId }: CheckoutClientProps) {
  const router = useRouter();
  const [clientSecret, setClientSecret] = useState<string>('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);

  // 获取翻译
  const t = useTranslations('checkout');

  useEffect(() => {
    createPaymentIntent();
  }, [orderId]);

  const createPaymentIntent = async () => {
    try {
      const response = await fetch('/api/stripe/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      });

      const data = await response.json();

      if (data.success) {
        setClientSecret(data.data.clientSecret);
      } else {
        setError(data.message || t('payment_error_title'));
      }
    } catch (error) {
      console.error('创建支付意图失败:', error);
      setError(t('payment_error_title'));
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = async () => {
    try {
      // 调用订单支付接口确保状态更新
      const response = await apiClient.processOrderPayment(orderId, {
        paymentMethod: 'stripe'
      });

      if (response.success) {
        console.log('订单状态更新成功:', response.data);
      } else {
        console.warn('订单状态更新失败:', response.error);
        // 即使状态更新失败，也继续跳转，因为Webhook会处理
      }
    } catch (error) {
      console.error('调用订单支付接口失败:', error);
      // 即使接口调用失败，也继续跳转，因为Webhook会处理
    }

    // 使用当前语言环境进行路由跳转
    router.push(`/submit/launch-date-success?toolId=${order.toolId}&paid=true`);
  };

  if (loading) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('creating_payment_session')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{t('payment_error_title')}</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/submit')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t('back_to_submit')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <CreditCard className="h-12 w-12 text-blue-600 mx-auto mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('page_title')}
        </h1>
        <p className="text-lg text-gray-600">
          {t('page_subtitle')}
        </p>
      </div>

      {/* Order Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('order_details')}</h2>

        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">{t('service_type')}</span>
            <span className="font-medium">{t('tool_priority_launch')}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">{t('tool_name')}</span>
            <span className="font-medium">{order.tool?.name || t('loading_placeholder')}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">{t('launch_date')}</span>
            <span className="font-medium">
              {order.selectedLaunchDate ? new Date(order.selectedLaunchDate).toLocaleDateString() : t('loading_placeholder')}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">{t('order_number')}</span>
            <span className="font-medium text-sm">{order._id}</span>
          </div>

          <hr className="my-4" />

          <div className="flex justify-between text-lg font-semibold">
            <span>{t('total')}</span>
            <span className="text-blue-600">{formatStripeAmount(order.amount)}</span>
          </div>
        </div>
      </div>

      {/* Service Features */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">
          {t('priority_service_title')}
        </h3>
        <ul className="space-y-2">
          <li className="flex items-center text-blue-800">
            <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
            {t('feature_any_date')}
          </li>
          <li className="flex items-center text-blue-800">
            <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
            {t('feature_priority_review')}
          </li>
          <li className="flex items-center text-blue-800">
            <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
            {t('feature_homepage_featured')}
          </li>
          <li className="flex items-center text-blue-800">
            <CheckCircle className="h-4 w-4 mr-2 flex-shrink-0" />
            {t('feature_dedicated_support')}
          </li>
        </ul>
      </div>

      {/* Security Notice */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
        <div className="flex items-center text-gray-700">
          <Shield className="h-5 w-5 mr-2 text-green-500" />
          <span className="text-sm">
            {t('security_notice')}
          </span>
        </div>
      </div>

      {/* Stripe Payment Form */}
      {clientSecret && (
        <Elements
          stripe={stripePromise}
          options={{
            clientSecret,
            appearance: {
              theme: 'stripe',
              variables: {
                colorPrimary: '#2563eb',
              }
            }
          }}
        >
          <StripeCheckoutForm
            onSuccess={handlePaymentSuccess}
            amount={order.amount}
          />
        </Elements>
      )}

      <p className="text-gray-500 text-sm mt-4 text-center">
        {t('terms_notice')}
      </p>
    </div>
  );
}
