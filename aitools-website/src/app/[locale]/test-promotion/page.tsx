'use client';

import React from 'react';
import LaunchDateSelector from '@/components/LaunchDateSelector';
import { 
  PRICING_CONFIG, 
  formatPrice, 
  formatOriginalPrice,
  hasActivePromotion,
  getPromotionInfo
} from '@/constants/pricing';

export default function TestPromotionPage() {
  const handleSubmit = async (option: 'free' | 'paid', date: string) => {
    console.log('Test submission:', { option, date });
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">限时优惠测试页面</h1>
      
      {/* 优惠信息展示 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">当前优惠配置</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">优惠状态</h3>
            <div className="space-y-1 text-sm">
              <p>优惠启用: {hasActivePromotion() ? '是' : '否'}</p>
              <p>优惠描述: {getPromotionInfo().description}</p>
              <p>折扣百分比: {getPromotionInfo().discountPercent}%</p>
              <p>剩余名额: {getPromotionInfo().remainingSlots}</p>
            </div>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">价格对比</h3>
            <div className="space-y-1 text-sm">
              <p>原价: {formatOriginalPrice(PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice)}</p>
              <p>现价: {formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice)}</p>
              <p>节省: ¥{PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice - PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice}</p>
            </div>
          </div>
        </div>
      </div>

      {/* 发布日期选择器 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-6">发布日期选择器（含限时优惠）</h2>
        
        <LaunchDateSelector
          currentOption="free"
          isEditing={false}
          onSubmit={handleSubmit}
          isSubmitting={false}
          hasPaidOrder={false}
        />
      </div>
    </div>
  );
}
