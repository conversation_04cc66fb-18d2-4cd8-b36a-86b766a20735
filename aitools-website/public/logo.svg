<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- 背景渐变：深紫 → 浅紫蓝 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#6B23FF"/>   <!-- 更浓烈的科技紫 -->
      <stop offset="100%" stop-color="#8E8DFF"/> <!-- 柔和蓝紫过渡 -->
    </linearGradient>
  </defs>

  <!-- 圆角背景 -->
  <rect x="0" y="0" width="200" height="200" rx="32" fill="url(#bgGradient)" />

  <!-- “AI”文字 -->
  <text x="28" y="128" font-size="72" font-family="Inter, Segoe UI, Helvetica Neue, sans-serif"
        fill="white" font-weight="700" letter-spacing="-1">
    AI
  </text>

  <!-- 网格点阵：9个工具图标小圆点 -->
  <g fill="rgba(255,255,255,0.9)" transform="translate(135,58)">
    <circle cx="0" cy="0" r="5"/>
    <circle cx="0" cy="20" r="5"/>
    <circle cx="0" cy="40" r="5"/>
    <circle cx="20" cy="0" r="5"/>
    <circle cx="20" cy="20" r="5"/>
    <circle cx="20" cy="40" r="5"/>
    <circle cx="40" cy="0" r="5"/>
    <circle cx="40" cy="20" r="5"/>
    <circle cx="40" cy="40" r="5"/>
  </g>
</svg>
