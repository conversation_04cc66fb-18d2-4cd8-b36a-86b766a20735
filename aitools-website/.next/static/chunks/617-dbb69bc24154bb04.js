"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[617],{617:(e,t,l)=>{l.d(t,{A:()=>m});var a=l(5155);l(2115);var i=l(2388),s=l(3786),r=l(2657),n=l(1976),c=l(4601),o=l(9651),d=l(3467),u=l(7652);let m=e=>{let{tool:t,onLoginRequired:l,onUnlike:m,isInLikedPage:h=!1,locale:x="en"}=e,g=(0,u.c3)("common");return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",style:{height:"100%"},children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[t.logo?(0,a.jsx)(o.Ay,{src:t.logo,alt:"".concat(t.name," logo"),width:o.iu.toolLogo.width,height:o.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:o.ng.toolLogo,placeholder:"blur"}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:t.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((0,d.Ef)(t.pricing)),children:(0,d.mV)(t.pricing)})]})]}),(0,a.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(s.A,{className:"h-5 w-5"})})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:t.description}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[t.tags.slice(0,3).map((e,t)=>(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),t.tags.length>3&&(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",t.tags.length-3]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(r.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.views})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.likes})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.default,{toolId:t._id,initialLikes:t.likes,initialLiked:h,onLoginRequired:l,onUnlike:m,isInLikedPage:h}),(0,a.jsx)(i.N_,{href:"/tools/".concat(t._id),className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:g("view_details")})]})]})]})})}},2388:(e,t,l)=>{l.d(t,{N_:()=>r,a8:()=>c,rd:()=>o});var a=l(9984),i=l(981);let s=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:r,redirect:n,usePathname:c,useRouter:o}=(0,i.A)(s)},3467:(e,t,l)=>{l.d(t,{$g:()=>d,Ef:()=>c,Y$:()=>n,kX:()=>a,mV:()=>o,tF:()=>u,v4:()=>r,vS:()=>i});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},i=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],s={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},r=[{value:"",label:"所有价格"},{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],n=[{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],c=e=>{switch(e){case s.FREE.value:return s.FREE.color;case s.FREEMIUM.value:return s.FREEMIUM.color;case s.PAID.value:return s.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case s.FREE.value:return s.FREE.label;case s.FREEMIUM.value:return s.FREEMIUM.label;case s.PAID.value:return s.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4601:(e,t,l)=>{l.d(t,{default:()=>d});var a=l(5155),i=l(2115),s=l(2388),r=l(7652),n=l(2108),c=l(9911),o=l(6214);function d(e){let{toolId:t,initialLikes:l=0,initialLiked:d=!1,onLoginRequired:u,onUnlike:m,isInLikedPage:h=!1,showCount:x=!0,size:g="md"}=e,{data:b}=(0,n.useSession)(),{getToolState:v,initializeToolState:p,toggleLike:f}=(0,o.X)(),w=(0,s.a8)(),I=(0,r.c3)("common");null==w||w.startsWith("/en");let j=v(t);(0,i.useEffect)(()=>{p(t,l,d)},[t,l,d]);let E=async()=>{if(!b){null==u||u();return}if(j.loading)return;let e=j.liked;await f(t,h)&&h&&e&&m&&m(t)},N=(()=>{switch(g){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,a.jsxs)("button",{onClick:E,disabled:j.loading,className:"\n        ".concat(N.button,"\n        inline-flex items-center space-x-1\n        ").concat(j.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500","\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      "),title:I(j.liked?"unlike":"like"),children:[j.loading?(0,a.jsx)("div",{className:"".concat(N.icon," animate-spin rounded-full border-2 border-gray-300 border-t-red-500")}):j.liked?(0,a.jsx)(c.Mbv,{className:N.icon}):(0,a.jsx)(c.sOK,{className:N.icon}),x&&(0,a.jsx)("span",{className:"".concat(N.text," font-medium"),children:j.likes})]})}},6214:(e,t,l)=>{l.d(t,{LikeProvider:()=>c,X:()=>o});var a=l(5155),i=l(2115),s=l(2108);let r={liked:!1,likes:0,loading:!1},n=(0,i.createContext)(null);function c(e){let{children:t}=e,{data:l}=(0,s.useSession)(),[c,o]=(0,i.useState)({}),d=(0,i.useCallback)(e=>c[e]||r,[c]),u=(0,i.useCallback)(function(e,t){let l=arguments.length>2&&void 0!==arguments[2]&&arguments[2];o(a=>a[e]?a:{...a,[e]:{liked:l,likes:t,loading:!1}})},[]),m=(0,i.useCallback)(async e=>{if(l)try{let t=await fetch("/api/tools/".concat(e,"/like"));if(t.ok){let l=await t.json();l.success&&o(t=>({...t,[e]:{liked:l.data.liked,likes:l.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[l]),h=(0,i.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!l)return!1;o(t=>({...t,[e]:{...t[e]||r,loading:!0}}));try{let l=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(l.ok){let t=await l.json();if(t.success)return o(l=>({...l,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return o(t=>({...t,[e]:{...t[e]||r,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),o(t=>({...t,[e]:{...t[e]||r,loading:!1}})),!1}},[l]);return(0,i.useEffect)(()=>{l?Object.keys(c).forEach(e=>{m(e)}):o(e=>{let t={};return Object.keys(e).forEach(l=>{t[l]={...e[l],liked:!1,loading:!1}}),t})},[l]),(0,a.jsx)(n.Provider,{value:{toolStates:c,toggleLike:h,getToolState:d,initializeToolState:u,refreshToolState:m},children:t})}function o(){let e=(0,i.useContext)(n);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},9651:(e,t,l)=>{l.d(t,{Ay:()=>s,iu:()=>r,ng:()=>n});var a=l(5155);l(2115);var i=l(6766);function s(e){let{src:t,alt:l,width:s,height:r,className:n="",priority:c=!1,fill:o=!1,sizes:d,placeholder:u="empty",blurDataURL:m,fallbackSrc:h="/images/placeholder.svg"}=e,x={src:t,alt:l,className:n,priority:c,placeholder:"blur"===u?"blur":"empty",blurDataURL:m||("blur"===u?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==":void 0),sizes:d||(o?"100vw":void 0)};return o?(0,a.jsx)("div",{className:"relative overflow-hidden",children:(0,a.jsx)(i.default,{...x,fill:!0,style:{objectFit:"contain",padding:2}})}):(0,a.jsx)(i.default,{...x,width:s,height:r})}let r={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:128,height:128},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},n={avatar:"40px",toolLogo:"52px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}}}]);