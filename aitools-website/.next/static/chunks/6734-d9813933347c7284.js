"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6734],{981:(e,t,r)=>{r.d(t,{A:()=>f});var n=r(5695),a=r(2115),o=r.t(a,2),c=r(3385),s=o["use".trim()],l=r(3225),i=r(6160),u=r(469),d=r(5155),h=r(8986);function f(e){let{Link:t,config:r,getPathname:o,...f}=function(e,t){var r,o,c;let h={...r=t||{},localePrefix:"object"==typeof(c=r.localePrefix)?c:{mode:c||"always"},localeCookie:!!((o=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof o&&o},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},f=h.pathnames,m=(0,a.forwardRef)(function({href:t,locale:r,...n},a){let o,c;"object"==typeof t?(o=t.pathname,c=t.params):o=t;let u=(0,l._x)(t),m=e(),y=(0,l.yL)(m)?s(m):m,g=u?p({locale:r||y,href:null==f?o:{pathname:o,params:c},forcePrefix:null!=r||void 0}):o;return(0,d.jsx)(i.default,{ref:a,href:"object"==typeof t?{...t,pathname:g}:g,locale:r,localeCookie:h.localeCookie,...n})});function p(e){let t,{forcePrefix:r,href:n,locale:a}=e;return null==f?"object"==typeof n?(t=n.pathname,n.query&&(t+=(0,u.Zn)(n.query))):t=n:t=(0,u.FP)({locale:a,...(0,u.TK)(n),pathnames:h.pathnames}),(0,u.x3)(t,a,h,r)}function y(e){return function(t,...r){return e(p(t),...r)}}return{config:h,Link:m,redirect:y(n.redirect),permanentRedirect:y(n.permanentRedirect),getPathname:p}}(c.Ym,e);return{...f,Link:t,usePathname:function(){let e=function(e){let t=(0,n.usePathname)(),r=(0,c.Ym)();return(0,a.useMemo)(()=>{if(!t)return t;let n=t,a=(0,l.XP)(r,e.localePrefix);if((0,l.wO)(a,t))n=(0,l.MY)(t,a);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,l.bL)(r);(0,l.wO)(e,t)&&(n=(0,l.MY)(t,e))}return n},[e.localePrefix,r,t])}(r),t=(0,c.Ym)();return(0,a.useMemo)(()=>e&&r.pathnames?(0,u.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,n.useRouter)(),t=(0,c.Ym)(),s=(0,n.usePathname)();return(0,a.useMemo)(()=>{function n(e){return function(n,a){let{locale:c,...l}=a||{},i=[o({href:n,locale:c||t})];Object.keys(l).length>0&&i.push(l),e(...i),(0,h.A)(r.localeCookie,s,t,c)}}return{...e,push:n(e.push),replace:n(e.replace),prefetch:n(e.prefetch)}},[t,s,e])},getPathname:o}}},1007:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2388:(e,t,r)=>{r.d(t,{N_:()=>c,a8:()=>l,rd:()=>i});var n=r(9984),a=r(981);let o=(0,n.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:c,redirect:s,usePathname:l,useRouter:i}=(0,a.A)(o)},2731:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(5155);function a(e){let{size:t="md",className:r=""}=e;return(0,n.jsx)("div",{className:"flex justify-center items-center ".concat(r),children:(0,n.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},2839:(e,t,r)=>{r.d(t,{u:()=>u});var n=r(9509);function a(){if(n.env.NEXT_PUBLIC_APP_URL)return n.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:t,port:r}=window.location;return"".concat(e,"//").concat(t).concat(r?":".concat(r):"")}}function o(){if(n.env.NEXT_PUBLIC_API_BASE_URL)return n.env.NEXT_PUBLIC_API_BASE_URL;let e=a();return"".concat(e,"/api")}function c(){return"production"}function s(){return"development"===c()}a(),o(),n.env.NEXTAUTH_URL?n.env.NEXTAUTH_URL:a(),c(),s(),c(),window.location.port||window.location.protocol,s();let l=o();class i{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r="".concat(this.baseURL).concat(e),n={headers:{"Content-Type":"application/json",...t.headers},...t};console.log("API request:",{url:r,config:n});let a=await fetch(r,n),o=await a.json();if(!a.ok)throw Error(o.error||"HTTP error! status: ".concat(a.status));return o}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,n]=e;void 0!==n&&t.append(r,n.toString())});let r=t.toString();return this.request("/tools".concat(r?"?".concat(r):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,n]=e;void 0!==n&&t.append(r,n.toString())});let r=t.toString();return this.request("/user/liked-tools".concat(r?"?".concat(r):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,n]=e;void 0!==n&&t.append(r,n.toString())});let r=t.toString();return this.request("/admin/tools".concat(r?"?".concat(r):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request("/orders/".concat(e))}async processOrderPayment(e,t){return this.request("/orders/".concat(e,"/pay"),{method:"POST",body:JSON.stringify(t)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}constructor(e=l){this.baseURL=e}}let u=new i},4416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},9783:(e,t,r)=>{r.d(t,{default:()=>c});var n=r(5155),a=r(5339),o=r(4416);function c(e){let{message:t,onClose:r,className:c=""}=e;return(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(c),children:(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)("p",{className:"text-red-800 text-sm",children:t})}),r&&(0,n.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,n.jsx)(o.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,r)=>{r.d(t,{A:()=>d});var n=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),c=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:c,className:u="",children:d,iconNode:h,...f}=e;return(0,n.createElement)("svg",{ref:t,...i,width:a,height:a,stroke:r,strokeWidth:c?24*Number(o)/Number(a):o,className:s("lucide",u),...!d&&!l(f)&&{"aria-hidden":"true"},...f},[...h.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:l,...i}=r;return(0,n.createElement)(u,{ref:o,iconNode:t,className:s("lucide-".concat(a(c(e))),"lucide-".concat(e),l),...i})});return r.displayName=c(e),r}},9984:(e,t,r)=>{r.d(t,{A:()=>n});function n(e){return e}}}]);