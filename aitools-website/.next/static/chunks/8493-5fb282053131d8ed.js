(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8493],{2388:(e,s,t)=>{"use strict";t.d(s,{N_:()=>i,a8:()=>o,rd:()=>c});var l=t(9984),a=t(981);let r=(0,l.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:i,redirect:n,usePathname:o,useRouter:c}=(0,a.A)(r)},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var l=t(5155);function a(e){let{size:s="md",className:t=""}=e;return(0,l.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,l.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3467:(e,s,t)=>{"use strict";t.d(s,{$g:()=>d,Ef:()=>o,Y$:()=>n,kX:()=>l,mV:()=>c,tF:()=>m,v4:()=>i,vS:()=>a});let l={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:l.FREE_LAUNCH.description,price:l.FREE_LAUNCH.displayPrice,features:l.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:l.PRIORITY_LAUNCH.description,price:l.PRIORITY_LAUNCH.displayPrice,features:l.PRIORITY_LAUNCH.features,recommended:!0}],r={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"所有价格"},{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],n=[{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],o=e=>{switch(e){case r.FREE.value:return r.FREE.color;case r.FREEMIUM.value:return r.FREEMIUM.color;case r.PAID.value:return r.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case r.FREE.value:return r.FREE.label;case r.FREEMIUM.value:return r.FREEMIUM.label;case r.PAID.value:return r.PAID.label;default:return e}},d=(e,s)=>0===e?"zh"===s?"免费":"Free":"\xa5".concat(e),m=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:s.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4035:(e,s,t)=>{"use strict";t.d(s,{default:()=>v});var l=t(5155),a=t(2115),r=t(2108),i=t(2388),n=t(7652),o=t(2731),c=t(5734),d=t(6063),m=t(3467),u=t(7550),x=t(9869),g=t(1284),h=t(8164);let b=["ai_assistant","chatgpt","conversational_ai","smart_qa","language_model","writing_assistant","content_generation","copywriting","blog_writing","marketing_copy","image_generation","image_editing","ai_painting","avatar_generation","background_removal","video_generation","video_editing","video_clipping","short_video_creation","video_subtitles","speech_synthesis","speech_recognition","music_generation","speech_to_text","text_to_speech","code_generation","code_completion","code_review","development_assistant","low_code_platform","data_analysis","data_visualization","business_intelligence","machine_learning","deep_learning","office_automation","document_processing","project_management","team_collaboration","note_taking","ui_design","logo_design","web_design","graphic_design","prototype_design","seo_optimization","social_media_marketing","email_marketing","content_marketing","market_analysis","machine_translation","real_time_translation","document_translation","voice_translation"];var p=t(4416),f=t(7924),j=t(3332);function y(e){let{selectedTags:s,onTagsChange:t,maxTags:r=3,placeholder:o}=e,[c,d]=(0,a.useState)(""),[m,u]=(0,a.useState)(!1),x=(0,i.a8)(),g=(0,n.c3)("common"),h=function(){let e=(0,n.c3)("tags");return b.map(s=>({key:s,label:e(s)}))}();null==x||x.startsWith("/en");let y=e=>{s.includes(e)?t(s.filter(s=>s!==e)):s.length<r&&t([...s,e])},v=e=>{t(s.filter(s=>s!==e))},N=h.filter(e=>e.label.toLowerCase().includes(c.toLowerCase())&&!s.includes(e.key)),_=e=>{let s=h.find(s=>s.key===e);return s?s.label:e};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:g("select_tags")}),(0,l.jsx)("span",{className:"text-sm text-gray-500",children:g("selected_count",{count:s.length,max:r})})]}),s.length>0&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:g("selected_tags")}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:s.map(e=>(0,l.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[_(e),(0,l.jsx)("button",{type:"button",onClick:()=>v(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,l.jsx)(p.A,{className:"h-3 w-3"})})]},e))})]}),(0,l.jsx)("div",{className:"space-y-3",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:g("select_tags_max",{max:r})}),(0,l.jsxs)("div",{className:"relative mb-3",children:[(0,l.jsx)("input",{type:"text",placeholder:o||g("search_tags"),value:c,onChange:e=>d(e.target.value),onFocus:()=>u(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,l.jsx)(f.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(m||c)&&(0,l.jsx)("div",{className:"relative",children:(0,l.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:N.length>0?(0,l.jsxs)("div",{className:"p-2",children:[(0,l.jsx)("div",{className:"grid grid-cols-1 gap-1",children:N.map(e=>{let t=s.length>=r;return(0,l.jsx)("button",{type:"button",onClick:()=>{y(e.key),d(""),u(!1)},disabled:t,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(t?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(j.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e.label]})},e.key)})}),N.length>50&&(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:g("found_tags",{count:N.length})})]}):(0,l.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:g(c?"no_tags_found":"start_typing")})})})]})}),(m||c)&&(0,l.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{u(!1),d("")}}),s.length>=r&&(0,l.jsx)("p",{className:"text-sm text-amber-600",children:g("max_tags_limit",{max:r})})]})}function v(e){let{categoryOptions:s,isEditMode:t=!1,toolId:b,initialTool:p}=e,f=(0,n.c3)("submit"),{data:j,status:v}=(0,r.useSession)(),N=(0,i.rd)(),[_,w]=(0,a.useState)(p||null),[k,E]=(0,a.useState)(t&&!p),[C,A]=(0,a.useState)({name:"",tagline:"",description:"",website:"",logoFile:null,category:"",tags:[],pricing:""}),[F,S]=(0,a.useState)(null),[I,R]=(0,a.useState)(""),[U,P]=(0,a.useState)(!1),[T,D]=(0,a.useState)(!1),[L,M]=(0,a.useState)("idle"),[q,O]=(0,a.useState)(""),[z,H]=(0,a.useState)({}),[Y,$]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if(!t||!b||p)return;let e=async()=>{try{let e=await fetch("/api/tools/".concat(b)),s=await e.json();if(s.success){let e=s.data;w(e),A({name:e.name||"",tagline:e.tagline||"",description:e.description||"",website:e.website||"",logoFile:null,category:e.category||"",tags:e.tags||[],pricing:e.pricing||""}),R(e.logo||""),S(e.logo||"")}else M("error"),O(s.message||"获取工具信息失败")}catch(e){console.error("获取工具信息失败:",e),M("error"),O("网络错误，请重试")}finally{E(!1)}};j?e():"loading"!==v&&E(!1)},[b,j,v,t,p]),(0,a.useEffect)(()=>{t&&p&&(w(p),A({name:p.name||"",tagline:p.tagline||"",description:p.description||"",website:p.website||"",logoFile:null,category:p.category||"",tags:p.tags||[],pricing:p.pricing||""}),R(p.logo||""),S(p.logo||""),E(!1))},[t,p]);let J=e=>{let{name:s,value:t}=e.target;A(e=>({...e,[s]:t}))},B=e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){A(e=>({...e,logoFile:t}));let e=new FileReader;e.onload=e=>{var s;S(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(t)}},X=()=>{let e={};return C.name.trim()||(e.name=f("form.tool_name")+" is required"),C.description.trim()||(e.description=f("form.description")+" is required"),C.website.trim()||(e.website=f("form.website_url")+" is required"),C.category||(e.category=f("form.category")+" is required"),C.pricing||(e.pricing=f("form.pricing_model")+" is required"),C.website&&!C.website.match(/^https?:\/\/.+/)&&(e.website=f("form.website_url_placeholder")),t||C.logoFile||(e.logo=f("form.logo_required")),0===C.tags.length&&(e.tags=f("form.tags_placeholder")),H(e),0===Object.keys(e).length},K=async e=>{var s;if(e.preventDefault(),!(null==j||null==(s=j.user)?void 0:s.email))return void $(!0);if(X()){D(!0),M("idle");try{let e=I;if(C.logoFile){let s=new FormData;s.append("logo",C.logoFile);let t=await fetch("/api/upload/logo",{method:"POST",body:s});if(t.ok)e=(await t.json()).data.url;else{let e=await t.json();throw Error(e.message||"Logo upload failed")}}if(t&&b){let s={name:C.name,tagline:C.tagline,description:C.description,website:C.website,logo:e||void 0,category:C.category,tags:C.tags,pricing:C.pricing},t=await fetch("/api/tools/".concat(b),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),l=await t.json();l.success?(M("success"),O("工具信息更新成功！"),setTimeout(()=>{N.push("/profile/submitted")},2e3)):(M("error"),O(l.error||"Update failed, please retry"))}else{let s={name:C.name,tagline:C.tagline,description:C.description,website:C.website,logoUrl:e,category:C.category,tags:C.tags,pricing:C.pricing},t=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(t.ok){let e=await t.json();M("success"),O(f("form.success_message")),setTimeout(()=>{N.push("/submit/tool-info-success?toolId=".concat(e.data.toolId))},2e3)}else{let e=await t.json();throw Error(e.message||"Submission failed")}}}catch(e){console.error("Submit error:",e),M("error"),O(f("form.error_message"))}finally{D(!1)}}};return"loading"===v||k?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)(o.A,{size:"lg"})}):j?t&&!_?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tool not found"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"The tool you want to edit does not exist or has been deleted."}),(0,l.jsx)(i.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"Back to Tools"})]})}):"success"===L?(0,l.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,l.jsx)(c.A,{message:q||f("form.success_message")})}):(0,l.jsxs)(a.Fragment,{children:[(0,l.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)(i.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,l.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Tools"]}),(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Tool"}),(0,l.jsx)("p",{className:"text-gray-600 mt-2",children:"Update your tool information to help more users understand your product."})]}),!t&&(0,l.jsxs)("div",{className:"text-center mb-8",children:[(0,l.jsx)("div",{className:"flex justify-center mb-4",children:(0,l.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,l.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})})}),(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:f("title")}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:f("subtitle")})]}),"error"===L&&(0,l.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,l.jsx)("p",{className:"text-red-800",children:q})}),(0,l.jsxs)("form",{onSubmit:K,className:"max-w-4xl mx-auto space-y-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,l.jsx)(g.A,{className:"h-5 w-5 mr-2 text-blue-600"}),f("form.basic_info")]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tool_name")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsx)("input",{type:"text",id:"name",name:"name",value:C.name,onChange:J,placeholder:f("form.tool_name_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tagline")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:C.tagline,onChange:J,placeholder:f("form.tagline_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.description")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsx)("textarea",{id:"description",name:"description",value:C.description,onChange:J,placeholder:f("form.description_placeholder"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.website_url")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,l.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),!t&&(0,l.jsx)("input",{type:"url",id:"website",name:"website",value:C.website,onChange:J,placeholder:t?"https://example.com":f("form.website_url_placeholder"),className:"w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(z.website?"border-red-300":"border-gray-300"),required:!0}),t&&(0,l.jsx)("div",{className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:C.website})]}),z.website&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:z.website})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.logo_upload")," ",!t&&(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,l.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;(null==(s=e.target.files)?void 0:s[0])&&B(e)},className:"hidden",id:"logo-upload",required:!t}),(0,l.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,l.jsx)(x.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:U?f("form.uploading"):f("form.click_to_upload")}),(0,l.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:f("form.logo_upload_hint")})]})]}),z.logo&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:z.logo})]}),F&&(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("div",{className:"border border-gray-300 rounded-md overflow-hidden w-24 h-24",children:(0,l.jsx)("img",{src:F,alt:f("form.logo_preview"),className:"w-full h-full object-cover"})})})]})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:f("form.category_and_pricing")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.category")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("select",{id:"category",name:"category",value:C.category,onChange:J,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,l.jsx)("option",{value:"",children:f("form.category_placeholder")}),s.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"pricing",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.pricing_model")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("select",{id:"pricing",name:"pricing",value:C.pricing,onChange:J,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(z.pricing?"border-red-300":"border-gray-300"),required:!0,children:[(0,l.jsx)("option",{value:"",children:f("form.pricing_placeholder")}),m.Y$.map(e=>(0,l.jsx)("option",{value:e.value,children:f("form.".concat(e.value))},e.value))]}),z.pricing&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:z.pricing})]})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tags")," ",(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsx)(y,{selectedTags:C.tags,onTagsChange:e=>{A(s=>({...s,tags:e}))},maxTags:3,placeholder:f("form.tags_placeholder")}),z.tags&&(0,l.jsx)("p",{className:"text-red-600 text-sm mt-1",children:z.tags})]})]}),!t&&(0,l.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:f("form.guidelines_title")}),(0,l.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_1")]}),(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_2")]}),(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_3")]}),(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_4")]}),(0,l.jsxs)("li",{className:"flex items-start",children:[(0,l.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_5")]})]})]}),(0,l.jsx)("div",{className:t?"flex justify-end":"flex justify-center",children:(0,l.jsx)("button",{type:"submit",disabled:T,className:"inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ".concat("px-8 py-3 text-base"),children:T?(0,l.jsxs)(a.Fragment,{children:[(0,l.jsx)(o.A,{size:"sm",className:"mr-2"}),f("form.submitting")]}):(0,l.jsxs)(a.Fragment,{children:[(0,l.jsx)(x.A,{className:"h-5 w-5 mr-2"}),f("form.submit_button")]})})})]})]}),(0,l.jsx)(d.A,{isOpen:Y,onClose:()=>$(!1)})]}):(0,l.jsxs)(a.Fragment,{children:[(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:f("auth.login_required")}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:f("auth.login_to_submit")}),(0,l.jsx)("button",{onClick:()=>$(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:f("auth.login")})]})}),(0,l.jsx)(d.A,{isOpen:Y,onClose:()=>$(!1)})]})}},5734:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var l=t(5155),a=t(646),r=t(4416);function i(e){let{message:s,onClose:t,className:i=""}=e;return(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(i),children:(0,l.jsxs)("div",{className:"flex items-start",children:[(0,l.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsx)("p",{className:"text-green-800 text-sm",children:s})}),t&&(0,l.jsx)("button",{onClick:t,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,l.jsx)(r.A,{className:"w-4 h-4"})})]})})}},6063:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var l=t(5155),a=t(2115),r=t(2108),i=t(2388),n=t(7652),o=t(3385),c=t(9911);function d(e){let{isOpen:s,onClose:t}=e,[d,m]=(0,a.useState)("method"),[u,x]=(0,a.useState)(""),[g,h]=(0,a.useState)(""),[b,p]=(0,a.useState)(!1),[f,j]=(0,a.useState)("");(0,i.a8)();let y=(0,n.c3)("auth");(0,o.Ym)();let v=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},N=()=>{m("method"),x(""),h(""),j(""),t()},_=async e=>{try{p(!0),await (0,r.signIn)(e,{callbackUrl:"/"})}catch(e){v(y("login_failed"),"error")}finally{p(!1)}},w=async()=>{if(!u)return void j(y("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u))return void j(y("email_invalid"));j(""),p(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:u})}),s=await e.json();s.success?(h(s.token),m("code"),v(y("verification_sent"))):v(s.error||y("send_failed"),"error")}catch(e){v(y("network_error"),"error")}finally{p(!1)}},k=async e=>{if(6===e.length){p(!0);try{let s=await (0,r.signIn)("email-code",{email:u,code:e,token:g,redirect:!1});(null==s?void 0:s.ok)?(v(y("login_success")),N()):v((null==s?void 0:s.error)||y("verification_error"),"error")}catch(e){v(y("network_error"),"error")}finally{p(!1)}}},E=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var l;null==(l=t[e+1])||l.focus()}let a=Array.from(t).map(e=>e.value).join("");6===a.length&&k(a)};return s?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:N}),(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===d&&y("login_title"),"email"===d&&y("email_login_title"),"code"===d&&y("verification_title")]}),(0,l.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(c.QCr,{})})]}),(0,l.jsxs)("div",{className:"p-6",children:["method"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:y("choose_method")}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>_("google"),disabled:b,children:[(0,l.jsx)(c.DSS,{}),y("google_login")]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>_("github"),disabled:b,children:[(0,l.jsx)(c.hL4,{}),y("github_login")]})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:y("or")})})]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>m("email"),children:[(0,l.jsx)(c.maD,{}),y("email_login")]})]}),"email"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:y("email_instruction")}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("email_address")}),(0,l.jsx)("input",{type:"email",value:u,onChange:e=>x(e.target.value),placeholder:y("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&w(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),f&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:w,disabled:b,children:b?y("sending"):y("send_code")}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("method"),children:y("back")})]})]}),"code"===d&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:y("verification_instruction",{email:u})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("verification_code")}),(0,l.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,l.jsx)("input",{type:"text",maxLength:1,onChange:s=>E(e,s.target.value),disabled:b,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("email"),children:y("resend_code")}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("method"),children:y("back")})]})]})]})]})]}):null}},8493:(e,s,t)=>{Promise.resolve().then(t.bind(t,6096)),Promise.resolve().then(t.bind(t,4035))}}]);