(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2455],{1094:(e,a,r)=>{Promise.resolve().then(r.bind(r,2724))},2724:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>g});var t=r(5155),s=r(2115),l=r(2108),i=r(2388),n=r(2731),c=r(9783),o=r(2839),d=r(7550),m=r(1007),u=r(9946);let x=(0,u.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),f=(0,u.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),h=(0,u.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),b=(0,u.A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var p=r(5525);function g(){var e,a,r,u;let{data:g,status:y,update:j}=(0,l.useSession)(),v=(0,i.rd)(),[N,w]=(0,s.useState)(!1),[k,A]=(0,s.useState)(""),[C,S]=(0,s.useState)(""),[M,E]=(0,s.useState)(!1),[z,V]=(0,s.useState)({name:"",email:"",bio:"",website:"",location:""}),D=s.useRef(null),[F,P]=(0,s.useState)({emailNotifications:!0,toolApprovalNotifications:!0,weeklyDigest:!1,marketingEmails:!1}),[_,T]=(0,s.useState)({profileVisibility:"public",showEmail:!1,showSubmittedTools:!0});(0,s.useEffect)(()=>{if("unauthenticated"===y)return void v.push("/");"authenticated"===y&&(null==g?void 0:g.user)&&H()},[y,g,v]);let H=async()=>{var e,a,r,t;try{let r=await o.u.getCurrentUser();r.success&&r.data?V({name:r.data.name||"",email:r.data.email||"",bio:r.data.bio||"",website:r.data.website||"",location:r.data.location||""}):V({name:(null==g||null==(e=g.user)?void 0:e.name)||"",email:(null==g||null==(a=g.user)?void 0:a.email)||"",bio:"",website:"",location:""})}catch(e){console.error("Failed to load user profile:",e),V({name:(null==g||null==(r=g.user)?void 0:r.name)||"",email:(null==g||null==(t=g.user)?void 0:t.email)||"",bio:"",website:"",location:""})}},O=async e=>{e.preventDefault(),w(!0),A(""),S("");try{let e=await o.u.updateProfile({name:z.name,bio:z.bio,website:z.website,location:z.location});e.success?(await j({...g,user:{...null==g?void 0:g.user,name:z.name}}),S("个人资料已更新")):A(e.error||"更新失败，请重试")}catch(e){console.error("Profile update error:",e),A("更新失败，请重试")}finally{w(!1)}},U=async e=>{var a;let r=null==(a=e.target.files)?void 0:a[0];if(r){if(!r.type.startsWith("image/"))return void A("请选择图片文件");if(r.size>5242880)return void A("图片文件大小不能超过5MB");E(!0),A("");try{let e=new FormData;e.append("avatar",r);let a=await fetch("/api/upload/avatar",{method:"POST",body:e}),t=await a.json();t.success?(await j({...g,user:{...null==g?void 0:g.user,image:t.data.avatarUrl}}),S("头像更新成功")):A(t.error||"头像更新失败")}catch(e){console.error("Avatar upload error:",e),A("头像上传失败，请重试")}finally{E(!1),D.current&&(D.current.value="")}}},q=async()=>{var e;if(null==g||null==(e=g.user)?void 0:e.image){E(!0),A("");try{let e=await o.u.updateProfile({avatar:""});e.success?(await j({...g,user:{...null==g?void 0:g.user,image:null}}),S("头像已删除")):A(e.error||"头像删除失败")}catch(e){console.error("Avatar delete error:",e),A("头像删除失败，请重试")}finally{E(!1)}}},B=async e=>{e.preventDefault(),w(!0),A(""),S("");try{await new Promise(e=>setTimeout(e,1e3)),S("通知设置已更新")}catch(e){A("更新失败，请重试")}finally{w(!1)}},L=async e=>{e.preventDefault(),w(!0),A(""),S("");try{await new Promise(e=>setTimeout(e,1e3)),S("隐私设置已更新")}catch(e){A("更新失败，请重试")}finally{w(!1)}};return"loading"===y?(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsx)(n.A,{size:"lg",className:"py-20"})}):g?(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center mb-8",children:[(0,t.jsx)(i.N_,{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)(d.A,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"账户设置"}),(0,t.jsx)("p",{className:"text-lg text-gray-600",children:"管理您的个人资料和偏好设置"})]})]}),C&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,t.jsx)("p",{className:"text-green-800",children:C})}),k&&(0,t.jsx)(c.default,{message:k,onClose:()=>A(""),className:"mb-6"}),(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-6",children:[(0,t.jsx)(m.A,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"个人资料"})]}),(0,t.jsxs)("form",{onSubmit:O,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"头像"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(e=g.user)?void 0:e.image)?(0,t.jsx)("img",{src:g.user.image,alt:g.user.name||"",className:"w-full h-full object-cover"}):(0,t.jsx)("span",{className:"text-xl font-medium text-gray-600",children:(null==(r=g.user)||null==(a=r.name)?void 0:a.charAt(0))||"U"})}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)("button",{type:"button",onClick:()=>{var e;null==(e=D.current)||e.click()},disabled:M,className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[M?(0,t.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,t.jsx)(x,{className:"mr-2 h-4 w-4"}),M?"上传中...":"更换头像"]}),(null==(u=g.user)?void 0:u.image)&&(0,t.jsxs)("button",{type:"button",onClick:q,disabled:M,className:"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,t.jsx)(f,{className:"mr-2 h-4 w-4"}),"删除"]})]})]}),(0,t.jsx)("input",{ref:D,type:"file",accept:"image/*",onChange:U,className:"hidden"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"姓名"}),(0,t.jsx)("input",{type:"text",id:"name",value:z.name,onChange:e=>V(a=>({...a,name:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱"}),(0,t.jsx)("input",{type:"email",id:"email",value:z.email,onChange:e=>V(a=>({...a,email:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-2",children:"个人简介"}),(0,t.jsx)("textarea",{id:"bio",rows:3,value:z.bio,onChange:e=>V(a=>({...a,bio:e.target.value})),placeholder:"介绍一下您自己...",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:"个人网站"}),(0,t.jsx)("input",{type:"url",id:"website",value:z.website,onChange:e=>V(a=>({...a,website:e.target.value})),placeholder:"https://example.com",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-gray-700 mb-2",children:"所在地"}),(0,t.jsx)("input",{type:"text",id:"location",value:z.location,onChange:e=>V(a=>({...a,location:e.target.value})),placeholder:"城市, 国家",className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)("button",{type:"submit",disabled:N,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[N?(0,t.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,t.jsx)(h,{className:"mr-2 h-5 w-5"}),"保存更改"]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-6",children:[(0,t.jsx)(b,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"通知设置"})]}),(0,t.jsxs)("form",{onSubmit:B,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"邮件通知"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"接收重要更新的邮件通知"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:F.emailNotifications,onChange:e=>P(a=>({...a,emailNotifications:e.target.checked})),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"工具审核通知"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"当您提交的工具审核状态变更时通知您"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:F.toolApprovalNotifications,onChange:e=>P(a=>({...a,toolApprovalNotifications:e.target.checked})),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"每周摘要"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"接收每周的新工具和热门内容摘要"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:F.weeklyDigest,onChange:e=>P(a=>({...a,weeklyDigest:e.target.checked})),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"营销邮件"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"接收产品更新和特别优惠信息"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:F.marketingEmails,onChange:e=>P(a=>({...a,marketingEmails:e.target.checked})),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,t.jsx)("div",{className:"flex justify-end pt-4",children:(0,t.jsxs)("button",{type:"submit",disabled:N,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[N?(0,t.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,t.jsx)(h,{className:"mr-2 h-5 w-5"}),"保存设置"]})})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-6",children:[(0,t.jsx)(p.A,{className:"h-6 w-6 text-gray-600 mr-3"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"隐私设置"})]}),(0,t.jsxs)("form",{onSubmit:L,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"个人资料可见性"}),(0,t.jsxs)("select",{value:_.profileVisibility,onChange:e=>T(a=>({...a,profileVisibility:e.target.value})),className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,t.jsx)("option",{value:"public",children:"公开 - 任何人都可以查看"}),(0,t.jsx)("option",{value:"private",children:"私密 - 只有您可以查看"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"显示邮箱地址"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"在您的公开资料中显示邮箱地址"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:_.showEmail,onChange:e=>T(a=>({...a,showEmail:e.target.checked})),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"显示提交的工具"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"在您的公开资料中显示您提交的工具"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:_.showSubmittedTools,onChange:e=>T(a=>({...a,showSubmittedTools:e.target.checked})),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)("button",{type:"submit",disabled:N,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[N?(0,t.jsx)(n.A,{size:"sm",className:"mr-2"}):(0,t.jsx)(h,{className:"mr-2 h-5 w-5"}),"保存设置"]})})]})]})]})]}):null}},7550:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[3385,6160,2108,6734,8441,1684,7358],()=>a(1094)),_N_E=e.O()}]);