(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[14],{681:(e,r,s)=>{"use strict";s.d(r,{default:()=>m});var a=s(5155),t=s(2115),l=s(7652),n=s(6932),c=s(6474),i=s(4653),o=s(5968);function d(e){let{tools:r,onFilteredToolsChange:s}=e,[d,u]=(0,t.useState)(""),[m,g]=(0,t.useState)(""),[x,h]=(0,t.useState)("popular"),[b,p]=(0,t.useState)("grid"),[v,f]=(0,t.useState)(!1),j=(0,l.c3)("category_page"),w=[{value:"",label:j("pricing_all")},{value:"free",label:j("pricing_free")},{value:"freemium",label:j("pricing_freemium")},{value:"paid",label:j("pricing_paid")}],N=[{value:"popular",label:j("sort_popular")},{value:"newest",label:j("sort_newest")},{value:"name",label:j("sort_name")},{value:"views",label:j("sort_views")}];return t.useEffect(()=>{s([...r.filter(e=>{let r=e.name.toLowerCase().includes(d.toLowerCase())||e.description.toLowerCase().includes(d.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(d.toLowerCase())),s=!m||e.pricing===m;return r&&s})].sort((e,r)=>{switch(x){case"popular":return(r.likes||0)-(e.likes||0);case"views":return(r.views||0)-(e.views||0);case"name":return e.name.localeCompare(r.name);case"newest":if(e.createdAt&&r.createdAt)return new Date(r.createdAt).getTime()-new Date(e.createdAt).getTime();return 0;default:return 0}}),b,d)},[r,d,m,x,b,s]),(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)("input",{type:"text",placeholder:j("search_placeholder"),value:d,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(n.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"md:hidden mb-4",children:(0,a.jsxs)("button",{onClick:()=>f(!v),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),j("filter_options"),(0,a.jsx)(c.A,{className:"ml-2 h-4 w-4 transform ".concat(v?"rotate-180":"")})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 ".concat(v?"block":"hidden md:grid"),children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:j("pricing")}),(0,a.jsx)("select",{value:m,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:w.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:j("sort")}),(0,a.jsx)("select",{value:x,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:N.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:j("view")}),(0,a.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,a.jsx)("button",{onClick:()=>p("grid"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ".concat("grid"===b?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,a.jsx)(i.A,{className:"h-4 w-4 mx-auto"})}),(0,a.jsx)("button",{onClick:()=>p("list"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ".concat("list"===b?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,a.jsx)(o.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]})})}var u=s(617);function m(e){let{tools:r}=e,[s,c]=(0,t.useState)(r),[i,o]=(0,t.useState)("grid"),[m,g]=(0,t.useState)(""),x=(0,l.c3)("category_page"),h=(0,t.useCallback)((e,r,s)=>{c(e),o(r),g(s)},[]);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d,{tools:r,onFilteredToolsChange:h}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("p",{className:"text-gray-600",children:[x("results_count",{count:s.length}),m&&" ".concat(x("search_for",{term:m}))]})}),s.length>0?(0,a.jsx)("div",{className:"grid"===i?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:s.map(e=>(0,a.jsx)(u.A,{tool:e},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(n.A,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:x("no_results_title")}),(0,a.jsx)("p",{className:"text-gray-600",children:x("no_results_desc")})]})]})}},6096:(e,r,s)=>{"use strict";s.d(r,{default:()=>l});var a=s(3385),t=s(5155);function l(e){let{locale:r,...s}=e;if(!r)throw Error(void 0);return(0,t.jsx)(a.Dk,{locale:r,...s})}},7361:(e,r,s)=>{Promise.resolve().then(s.bind(s,6160)),Promise.resolve().then(s.bind(s,6096)),Promise.resolve().then(s.bind(s,681)),Promise.resolve().then(s.bind(s,9783))},9783:(e,r,s)=>{"use strict";s.d(r,{default:()=>n});var a=s(5155),t=s(5339),l=s(4416);function n(e){let{message:r,onClose:s,className:n=""}=e;return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(n),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(t.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-red-800 text-sm",children:r})}),s&&(0,a.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6711,3385,6160,2108,8003,3467,617,8441,1684,7358],()=>r(7361)),_N_E=e.O()}]);