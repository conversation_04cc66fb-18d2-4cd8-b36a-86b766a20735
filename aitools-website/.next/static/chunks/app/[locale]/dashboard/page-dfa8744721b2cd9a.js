(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[758],{646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},981:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(5695),a=s(2115),l=s.t(a,2),n=s(3385),c=l["use".trim()],i=s(3225),o=s(6160),d=s(469),m=s(5155),h=s(8986);function x(e){let{Link:t,config:s,getPathname:l,...x}=function(e,t){var s,l,n;let h={...s=t||{},localePrefix:"object"==typeof(n=s.localePrefix)?n:{mode:n||"always"},localeCookie:!!((l=s.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof l&&l},localeDetection:s.localeDetection??!0,alternateLinks:s.alternateLinks??!0},x=h.pathnames,u=(0,a.forwardRef)(function({href:t,locale:s,...r},a){let l,n;"object"==typeof t?(l=t.pathname,n=t.params):l=t;let d=(0,i._x)(t),u=e(),g=(0,i.yL)(u)?c(u):u,f=d?p({locale:s||g,href:null==x?l:{pathname:l,params:n},forcePrefix:null!=s||void 0}):l;return(0,m.jsx)(o.default,{ref:a,href:"object"==typeof t?{...t,pathname:f}:f,locale:s,localeCookie:h.localeCookie,...r})});function p(e){let t,{forcePrefix:s,href:r,locale:a}=e;return null==x?"object"==typeof r?(t=r.pathname,r.query&&(t+=(0,d.Zn)(r.query))):t=r:t=(0,d.FP)({locale:a,...(0,d.TK)(r),pathnames:h.pathnames}),(0,d.x3)(t,a,h,s)}function g(e){return function(t,...s){return e(p(t),...s)}}return{config:h,Link:u,redirect:g(r.redirect),permanentRedirect:g(r.permanentRedirect),getPathname:p}}(n.Ym,e);return{...x,Link:t,usePathname:function(){let e=function(e){let t=(0,r.usePathname)(),s=(0,n.Ym)();return(0,a.useMemo)(()=>{if(!t)return t;let r=t,a=(0,i.XP)(s,e.localePrefix);if((0,i.wO)(a,t))r=(0,i.MY)(t,a);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,i.bL)(s);(0,i.wO)(e,t)&&(r=(0,i.MY)(t,e))}return r},[e.localePrefix,s,t])}(s),t=(0,n.Ym)();return(0,a.useMemo)(()=>e&&s.pathnames?(0,d.aM)(t,e,s.pathnames):e,[t,e])},useRouter:function(){let e=(0,r.useRouter)(),t=(0,n.Ym)(),c=(0,r.usePathname)();return(0,a.useMemo)(()=>{function r(e){return function(r,a){let{locale:n,...i}=a||{},o=[l({href:r,locale:n||t})];Object.keys(i).length>0&&o.push(i),e(...o),(0,h.A)(s.localeCookie,c,t,n)}}return{...e,push:r(e.push),replace:r(e.replace),prefetch:r(e.prefetch)}},[t,c,e])},getPathname:l}}},2388:(e,t,s)=>{"use strict";s.d(t,{N_:()=>n,a8:()=>i,rd:()=>o});var r=s(9984),a=s(981);let l=(0,r.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:n,redirect:c,usePathname:i,useRouter:o}=(0,a.A)(l)},2657:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2731:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5155);function a(e){let{size:t="md",className:s=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},2839:(e,t,s)=>{"use strict";s.d(t,{u:()=>d});var r=s(9509);function a(){if(r.env.NEXT_PUBLIC_APP_URL)return r.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:t,port:s}=window.location;return"".concat(e,"//").concat(t).concat(s?":".concat(s):"")}}function l(){if(r.env.NEXT_PUBLIC_API_BASE_URL)return r.env.NEXT_PUBLIC_API_BASE_URL;let e=a();return"".concat(e,"/api")}function n(){return"production"}function c(){return"development"===n()}a(),l(),r.env.NEXTAUTH_URL?r.env.NEXTAUTH_URL:a(),n(),c(),n(),window.location.port||window.location.protocol,c();let i=l();class o{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...t.headers},...t};console.log("API request:",{url:s,config:r});let a=await fetch(s,r),l=await a.json();if(!a.ok)throw Error(l.error||"HTTP error! status: ".concat(a.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request("/orders/".concat(e))}async processOrderPayment(e,t){return this.request("/orders/".concat(e,"/pay"),{method:"POST",body:JSON.stringify(t)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}constructor(e=i){this.baseURL=e}}let d=new o},3717:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3786:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},3872:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(5155),a=s(2115),l=s(2388),n=s(2731),c=s(9783),i=s(2839),o=s(646),d=s(4186),m=s(4861),h=s(4616),x=s(2713),u=s(2657),p=s(9074),g=s(3786),f=s(3717);let y=e=>{switch(e){case"published":return"bg-green-100 text-green-800";case"approved":return"bg-blue-100 text-blue-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},j=e=>{switch(e){case"published":return"已发布";case"approved":return"已通过";case"pending":return"审核中";case"rejected":return"已拒绝";case"draft":return"草稿";default:return e}},b=e=>{switch(e){case"published":case"approved":return(0,r.jsx)(o.A,{className:"h-4 w-4"});case"pending":case"draft":return(0,r.jsx)(d.A,{className:"h-4 w-4"});case"rejected":return(0,r.jsx)(m.A,{className:"h-4 w-4"});default:return null}};function N(){let[e,t]=(0,a.useState)("all"),[s,d]=(0,a.useState)([]),[m,N]=(0,a.useState)(!0),[v,w]=(0,a.useState)("");(0,a.useEffect)(()=>{k()},[]);let k=async()=>{try{N(!0),w("");let e=await i.u.getAdminTools();e.success&&e.data?d(e.data.tools):w(e.error||"获取工具列表失败")}catch(e){w("网络错误，请重试")}finally{N(!1)}},A=s.filter(t=>"all"===e||t.status===e),P={total:s.length,approved:s.filter(e=>"approved"===e.status).length,pending:s.filter(e=>"pending"===e.status).length,rejected:s.filter(e=>"rejected"===e.status).length,totalViews:s.reduce((e,t)=>e+t.views,0),totalLikes:s.reduce((e,t)=>e+t.likes,0)};return m?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(n.A,{size:"lg",className:"py-20"})}):(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"开发者仪表板"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"管理您提交的 AI 工具"})]}),(0,r.jsxs)(l.N_,{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(h.A,{className:"mr-2 h-5 w-5"}),"提交新工具"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总提交数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(o.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.approved})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总点赞数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.totalLikes})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("button",{onClick:()=>t("all"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("all"===e?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["全部 (",P.total,")"]}),(0,r.jsxs)("button",{onClick:()=>t("approved"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("approved"===e?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["已通过 (",P.approved,")"]}),(0,r.jsxs)("button",{onClick:()=>t("pending"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("pending"===e?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["审核中 (",P.pending,")"]}),(0,r.jsxs)("button",{onClick:()=>t("rejected"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("rejected"===e?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["已拒绝 (",P.rejected,")"]})]})}),v&&(0,r.jsx)(c.default,{message:v,onClose:()=>w(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:A.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:A.map(e=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(y(e.status)),children:[b(e.status),(0,r.jsx)("span",{className:"ml-1",children:j(e.status)})]})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["提交于 ",new Date(e.submittedAt).toLocaleDateString("zh-CN")]})]}),e.launchDate&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["发布于 ",new Date(e.launchDate).toLocaleDateString("zh-CN")]})]}),"approved"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.views," 浏览"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"❤️"}),(0,r.jsxs)("span",{children:[e.likes," 点赞"]})]})]})]}),"rejected"===e.status&&e.reviewNotes&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-red-800",children:[(0,r.jsx)("strong",{children:"拒绝原因："})," ",e.reviewNotes]})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["approved"===e.status&&(0,r.jsx)(l.N_,{href:"/tools/".concat(e._id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"查看详情",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:"访问网站",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})}),("rejected"===e.status||"pending"===e.status)&&(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"编辑",children:(0,r.jsx)(f.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(x.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===e?"还没有提交任何工具":"没有".concat(j(e),"的工具")}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===e?"开始提交您的第一个 AI 工具吧！":"尝试选择其他状态查看工具"}),"all"===e&&(0,r.jsxs)(l.N_,{href:"/submit",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"提交工具"]})]})})]})}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7439:(e,t,s)=>{Promise.resolve().then(s.bind(s,3872))},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9783:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(5155),a=s(5339),l=s(4416);function n(e){let{message:t,onClose:s,className:n=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(n),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:t})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:m,iconNode:h,...x}=e;return(0,r.createElement)("svg",{ref:t,...o,width:a,height:a,stroke:s,strokeWidth:n?24*Number(l)/Number(a):l,className:c("lucide",d),...!m&&!i(x)&&{"aria-hidden":"true"},...x},[...h.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{className:i,...o}=s;return(0,r.createElement)(d,{ref:l,iconNode:t,className:c("lucide-".concat(a(n(e))),"lucide-".concat(e),i),...o})});return s.displayName=n(e),s}},9984:(e,t,s)=>{"use strict";function r(e){return e}s.d(t,{A:()=>r})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,8441,1684,7358],()=>t(7439)),_N_E=e.O()}]);