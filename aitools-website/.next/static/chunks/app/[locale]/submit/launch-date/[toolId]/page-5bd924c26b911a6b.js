(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[245],{646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},981:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var a=r(5695),l=r(2115),s=r.t(l,2),n=r(3385),i=s["use".trim()],c=r(3225),o=r(6160),d=r(469),u=r(5155),m=r(8986);function h(e){let{Link:t,config:r,getPathname:s,...h}=function(e,t){var r,s,n;let m={...r=t||{},localePrefix:"object"==typeof(n=r.localePrefix)?n:{mode:n||"always"},localeCookie:!!((s=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof s&&s},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},h=m.pathnames,p=(0,l.forwardRef)(function({href:t,locale:r,...a},l){let s,n;"object"==typeof t?(s=t.pathname,n=t.params):s=t;let d=(0,c._x)(t),p=e(),x=(0,c.yL)(p)?i(p):p,g=d?f({locale:r||x,href:null==h?s:{pathname:s,params:n},forcePrefix:null!=r||void 0}):s;return(0,u.jsx)(o.default,{ref:l,href:"object"==typeof t?{...t,pathname:g}:g,locale:r,localeCookie:m.localeCookie,...a})});function f(e){let t,{forcePrefix:r,href:a,locale:l}=e;return null==h?"object"==typeof a?(t=a.pathname,a.query&&(t+=(0,d.Zn)(a.query))):t=a:t=(0,d.FP)({locale:l,...(0,d.TK)(a),pathnames:m.pathnames}),(0,d.x3)(t,l,m,r)}function x(e){return function(t,...r){return e(f(t),...r)}}return{config:m,Link:p,redirect:x(a.redirect),permanentRedirect:x(a.permanentRedirect),getPathname:f}}(n.Ym,e);return{...h,Link:t,usePathname:function(){let e=function(e){let t=(0,a.usePathname)(),r=(0,n.Ym)();return(0,l.useMemo)(()=>{if(!t)return t;let a=t,l=(0,c.XP)(r,e.localePrefix);if((0,c.wO)(l,t))a=(0,c.MY)(t,l);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,c.bL)(r);(0,c.wO)(e,t)&&(a=(0,c.MY)(t,e))}return a},[e.localePrefix,r,t])}(r),t=(0,n.Ym)();return(0,l.useMemo)(()=>e&&r.pathnames?(0,d.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,a.useRouter)(),t=(0,n.Ym)(),i=(0,a.usePathname)();return(0,l.useMemo)(()=>{function a(e){return function(a,l){let{locale:n,...c}=l||{},o=[s({href:a,locale:n||t})];Object.keys(c).length>0&&o.push(c),e(...o),(0,m.A)(r.localeCookie,i,t,n)}}return{...e,push:a(e.push),replace:a(e.replace),prefetch:a(e.prefetch)}},[t,i,e])},getPathname:s}}},1586:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2388:(e,t,r)=>{"use strict";r.d(t,{N_:()=>n,a8:()=>c,rd:()=>o});var a=r(9984),l=r(981);let s=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:n,redirect:i,usePathname:c,useRouter:o}=(0,l.A)(s)},3467:(e,t,r)=>{"use strict";r.d(t,{$g:()=>d,Ef:()=>c,Y$:()=>i,kX:()=>a,mV:()=>o,tF:()=>u,v4:()=>n,vS:()=>l});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},l=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],s={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],i=[{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],c=e=>{switch(e){case s.FREE.value:return s.FREE.color;case s.FREEMIUM.value:return s.FREEMIUM.color;case s.PAID.value:return s.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case s.FREE.value:return s.FREE.label;case s.FREEMIUM.value:return s.FREEMIUM.label;case s.PAID.value:return s.PAID.label;default:return e}},d=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},6096:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(3385),l=r(5155);function s(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return(0,l.jsx)(a.Dk,{locale:t,...r})}},7652:(e,t,r)=>{"use strict";r.d(t,{c3:()=>s});var a=r(3385);function l(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let s=l(0,a.c3);l(0,a.kc)},7990:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var a=r(5155),l=r(2115),s=r(2388),n=r(7652),i=r(3385),c=r(9074),o=r(1586),d=r(646),u=r(4186),m=r(3467);let h=m.vS;function p(e){let{currentOption:t="free",currentDate:r,isEditing:s=!1,onSubmit:p,isSubmitting:f,error:x,hasPaidOrder:g=!1}=e,[b,y]=(0,l.useState)(g?"paid":t),[v,N]=(0,l.useState)(""),j=(0,n.c3)("launch"),E=(0,i.Ym)(),A=()=>{let e=new Date;return e.setMonth(e.getMonth()+1),e.toISOString().split("T")[0]},w=()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]};(0,l.useEffect)(()=>{r?N(r):"free"===b?N(A()):N(w())},[b,r]);let _=e=>{g||(y(e),"free"===e?N(A()):N(w()))},k=async()=>{v&&await p(b,v)};return(0,a.jsxs)("div",{className:"space-y-8",children:[!g&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:s?j("select_plan"):j("select_option")}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:h.map(e=>(0,a.jsxs)("div",{className:"relative border-2 rounded-lg p-6 cursor-pointer transition-all ".concat(b===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"," ").concat("recommended"in e&&e.recommended?"ring-2 ring-blue-200":""),onClick:()=>_(e.id),children:["recommended"in e&&e.recommended&&(0,a.jsx)("div",{className:"absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:j("recommended")}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:["free"===e.id?(0,a.jsx)(c.A,{className:"h-6 w-6 text-gray-600 mr-3"}):(0,a.jsx)(o.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:j("plans.".concat(e.id,".title"))}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:j("plans.".concat(e.id,".description"))})]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,m.$g)(e.price,E)})})]}),(0,a.jsx)("ul",{className:"space-y-2",children:e.features.map((t,r)=>(0,a.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-green-500 mr-2 flex-shrink-0"}),j("plans.".concat(e.id,".features.").concat(r))]},r))}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("input",{type:"radio",name:"launchOption",value:e.id,checked:b===e.id,onChange:()=>_(e.id),className:"sr-only"}),(0,a.jsx)("div",{className:"w-4 h-4 rounded-full border-2 ".concat(b===e.id?"border-blue-500 bg-blue-500":"border-gray-300"),children:b===e.id&&(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})})]})]},e.id))})]}),g&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-green-500 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-green-800",children:j("priority_service_activated_title")}),(0,a.jsx)("p",{className:"text-sm text-green-600 mt-1",children:j("priority_service_activated_description")})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2"}),j("select_date")]}),(0,a.jsxs)("div",{className:"max-w-md",children:[(0,a.jsx)("input",{type:"date",value:v,onChange:e=>N(e.target.value),min:g||"paid"===b?w():A(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:g?j("paid_date_info"):"free"===b?j("free_date_info"):j("paid_date_info")})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("button",{onClick:k,disabled:f||!v,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"paid"===b?j("processing"):j("saving")]}):(0,a.jsx)(a.Fragment,{children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),j("save_changes")]}):"paid"===b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),s?j("upgrade_and_pay",{price:(0,m.$g)(m.kX.PRIORITY_LAUNCH.displayPrice,E)}):j("pay_amount",{price:(0,m.$g)(m.kX.PRIORITY_LAUNCH.displayPrice,E)})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),s?j("save_changes"):j("confirm_date")]})})}),x&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-4",children:x}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-4",children:g?j("changes_effective"):"paid"===b?j("payment_redirect"):s?j("changes_effective"):j("review_queue")})]})]})}function f(e){let{toolId:t,locale:r,currentOption:i="free",currentDate:c,minFreeDate:o,minPaidDate:d,hasPaidOrder:u=!1,orderId:m,isEditMode:h=!1}=e,f=(0,s.rd)(),x=(0,n.c3)("launch"),[g,b]=(0,l.useState)(!1),[y,v]=(0,l.useState)(""),N=async(e,a)=>{b(!0),v("");try{let l=await fetch("/api/tools/".concat(t,"/launch-date"),{method:"POST",headers:{"Content-Type":"application/json","X-Locale":r},body:JSON.stringify({launchOption:e,selectedDate:a,hasPaidOrder:u})}),s=await l.json();s.success?"paid"===e&&s.data.paymentUrl?window.location.href=s.data.paymentUrl:f.push("/submit/launch-date-success?toolId=".concat(t)):v(s.message||x("submit_failed"))}catch(e){v(x("network_error"))}finally{b(!1)}};return(0,a.jsx)(p,{toolId:t,currentOption:u?"paid":i,currentDate:c,isEditing:h,onSubmit:N,isSubmitting:g,error:y,hasPaidOrder:u})}},9010:(e,t,r)=>{Promise.resolve().then(r.bind(r,6160)),Promise.resolve().then(r.bind(r,6096)),Promise.resolve().then(r.bind(r,7990))},9074:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:s=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:t,...o,width:l,height:l,stroke:r,strokeWidth:n?24*Number(s)/Number(l):s,className:i("lucide",d),...!u&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,s)=>{let{className:c,...o}=r;return(0,a.createElement)(d,{ref:s,iconNode:t,className:i("lucide-".concat(l(n(e))),"lucide-".concat(e),c),...o})});return r.displayName=n(e),r}},9984:(e,t,r)=>{"use strict";function a(e){return e}r.d(t,{A:()=>a})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,8441,1684,7358],()=>t(9010)),_N_E=e.O()}]);