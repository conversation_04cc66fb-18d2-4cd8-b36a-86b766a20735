{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/sitemap.xml", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}], "regex": "^/sitemap\\.xml(?:/)?$"}, {"source": "/robots.txt", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}], "regex": "^/robots\\.txt(?:/)?$"}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/_next/static(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/admin/tools/[id]/approve", "regex": "^/api/admin/tools/([^/]+?)/approve(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/tools/(?<nxtPid>[^/]+?)/approve(?:/)?$"}, {"page": "/api/admin/tools/[id]/reject", "regex": "^/api/admin/tools/([^/]+?)/reject(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/tools/(?<nxtPid>[^/]+?)/reject(?:/)?$"}, {"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/orders/[id]", "regex": "^/api/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/orders/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/orders/[id]/pay", "regex": "^/api/orders/([^/]+?)/pay(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/orders/(?<nxtPid>[^/]+?)/pay(?:/)?$"}, {"page": "/api/tools/[id]", "regex": "^/api/tools/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tools/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/tools/[id]/comments", "regex": "^/api/tools/([^/]+?)/comments(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tools/(?<nxtPid>[^/]+?)/comments(?:/)?$"}, {"page": "/api/tools/[id]/launch-date", "regex": "^/api/tools/([^/]+?)/launch\\-date(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tools/(?<nxtPid>[^/]+?)/launch\\-date(?:/)?$"}, {"page": "/api/tools/[id]/like", "regex": "^/api/tools/([^/]+?)/like(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tools/(?<nxtPid>[^/]+?)/like(?:/)?$"}, {"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/about", "regex": "^/([^/]+?)/about(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/about(?:/)?$"}, {"page": "/[locale]/admin", "regex": "^/([^/]+?)/admin(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin(?:/)?$"}, {"page": "/[locale]/admin/dashboard", "regex": "^/([^/]+?)/admin/dashboard(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/dashboard(?:/)?$"}, {"page": "/[locale]/admin/tools/[id]", "regex": "^/([^/]+?)/admin/tools/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/admin/tools/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/[locale]/categories", "regex": "^/([^/]+?)/categories(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/categories(?:/)?$"}, {"page": "/[locale]/categories/[slug]", "regex": "^/([^/]+?)/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/categories/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[locale]/contact", "regex": "^/([^/]+?)/contact(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/contact(?:/)?$"}, {"page": "/[locale]/dashboard", "regex": "^/([^/]+?)/dashboard(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/dashboard(?:/)?$"}, {"page": "/[locale]/payment/checkout", "regex": "^/([^/]+?)/payment/checkout(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/payment/checkout(?:/)?$"}, {"page": "/[locale]/privacy", "regex": "^/([^/]+?)/privacy(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/privacy(?:/)?$"}, {"page": "/[locale]/profile", "regex": "^/([^/]+?)/profile(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/profile(?:/)?$"}, {"page": "/[locale]/profile/liked", "regex": "^/([^/]+?)/profile/liked(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/profile/liked(?:/)?$"}, {"page": "/[locale]/profile/submitted", "regex": "^/([^/]+?)/profile/submitted(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/profile/submitted(?:/)?$"}, {"page": "/[locale]/search", "regex": "^/([^/]+?)/search(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/search(?:/)?$"}, {"page": "/[locale]/settings", "regex": "^/([^/]+?)/settings(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/settings(?:/)?$"}, {"page": "/[locale]/submit", "regex": "^/([^/]+?)/submit(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/submit(?:/)?$"}, {"page": "/[locale]/submit/edit/[toolId]", "regex": "^/([^/]+?)/submit/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPtoolId": "nxtPtoolId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/submit/edit/(?<nxtPtoolId>[^/]+?)(?:/)?$"}, {"page": "/[locale]/submit/launch-date/[toolId]", "regex": "^/([^/]+?)/submit/launch\\-date/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPtoolId": "nxtPtoolId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/submit/launch\\-date/(?<nxtPtoolId>[^/]+?)(?:/)?$"}, {"page": "/[locale]/submit/launch-date-success", "regex": "^/([^/]+?)/submit/launch\\-date\\-success(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/submit/launch\\-date\\-success(?:/)?$"}, {"page": "/[locale]/submit/success", "regex": "^/([^/]+?)/submit/success(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/submit/success(?:/)?$"}, {"page": "/[locale]/submit/tool-info-success", "regex": "^/([^/]+?)/submit/tool\\-info\\-success(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/submit/tool\\-info\\-success(?:/)?$"}, {"page": "/[locale]/terms", "regex": "^/([^/]+?)/terms(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/terms(?:/)?$"}, {"page": "/[locale]/test-auth", "regex": "^/([^/]+?)/test\\-auth(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-auth(?:/)?$"}, {"page": "/[locale]/test-pricing", "regex": "^/([^/]+?)/test\\-pricing(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-pricing(?:/)?$"}, {"page": "/[locale]/test-stripe", "regex": "^/([^/]+?)/test\\-stripe(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/test\\-stripe(?:/)?$"}, {"page": "/[locale]/tools", "regex": "^/([^/]+?)/tools(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/tools(?:/)?$"}, {"page": "/[locale]/tools/[id]", "regex": "^/([^/]+?)/tools/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPid": "nxtPid"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/tools/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}