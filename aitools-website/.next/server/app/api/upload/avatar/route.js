(()=>{var e={};e.id=6503,e.ids=[6503],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>c});var i=t(36344),s=t(65752),a=t(13581),o=t(75745),n=t(17063);let c={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,a.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,o.A)();let r=await n.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!r)return null;let t=r.emailVerificationToken;if(!t||!t.includes(":"))return null;let[i,s]=t.split(":");if(i!==e.token||s!==e.code)return null;return r.emailVerified=!0,r.emailVerificationToken=void 0,r.emailVerificationExpires=void 0,r.lastLoginAt=new Date,r.accounts.some(e=>"email"===e.provider)||r.accounts.push({provider:"email",providerId:"email",providerAccountId:r.email}),await r.save(),{id:r._id.toString(),email:r.email,name:r.name,image:r.avatar,role:r.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:r,profile:t}){if(r?.provider==="email-code")return!0;await (0,o.A)();try{let i=await n.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new n.A({email:e.email,name:e.name||t?.name||"User",avatar:e.image||t?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),r&&"email-code"!==r.provider&&(i.addAccount({provider:r.provider,providerId:r.provider,providerAccountId:r.providerAccountId||r.id||"",accessToken:r.access_token,refreshToken:r.refresh_token,expiresAt:r.expires_at?new Date(1e3*r.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:r})=>(r&&(e.userId=r.id,e.role=r.role||"user"),e),session:async({session:e,token:r})=>(r&&e.user&&(e.user.id=r.userId,e.user.role=r.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var i=t(56037),s=t.n(i);let a=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[a],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let r=this.accounts.find(r=>r.provider===e.provider&&r.providerAccountId===e.providerAccountId);r?Object.assign(r,e):this.accounts.push(e)},o.methods.removeAccount=function(e,r){this.accounts=this.accounts.filter(t=>t.provider!==e||t.providerAccountId!==r)};let n=s().models.User||s().model("User",o)},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{describeHasCheckingStringProperty:function(){return s},describeStringPropertyAccess:function(){return i},wellKnownProperties:function(){return a}});let t=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function i(e,r){return t.test(r)?"`"+e+"."+r+"`":"`"+e+"["+JSON.stringify(r)+"]`"}function s(e,r){let t=JSON.stringify(r);return"`Reflect.has("+e+", "+t+")`, `"+t+" in "+e+"`, or similar"}let a=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var i=t(56037),s=t.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let n=async function(){if(o.conn)return o.conn;o.promise||(o.promise=s().connect(a,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},89516:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var i={};t.r(i),t.d(i,{POST:()=>f});var s=t(96559),a=t(48088),o=t(37719),n=t(32190),c=t(35426),u=t(12909),l=t(75745),p=t(17063),d=t(79748),m=t(33873),v=t(29021);async function f(e){try{let r=await (0,c.getServerSession)(u.N);if(!r?.user?.email)return n.NextResponse.json({success:!1,error:"未登录"},{status:401});await (0,l.A)();let t=(await e.formData()).get("avatar");if(!t)return n.NextResponse.json({success:!1,error:"未选择文件"},{status:400});if(!t.type.startsWith("image/"))return n.NextResponse.json({success:!1,error:"只能上传图片文件"},{status:400});if(t.size>5242880)return n.NextResponse.json({success:!1,error:"文件大小不能超过5MB"},{status:400});let i=(0,m.join)(process.cwd(),"public","uploads","avatars");(0,v.existsSync)(i)||await (0,d.mkdir)(i,{recursive:!0});let s=Date.now(),a=t.name.split(".").pop(),o=`${r.user.email.replace("@","_").replace(".","_")}_${s}.${a}`,f=(0,m.join)(i,o),g=await t.arrayBuffer(),x=Buffer.from(g);await (0,d.writeFile)(f,x);let y=`/uploads/avatars/${o}`,h=await p.A.findOneAndUpdate({email:r.user.email},{avatar:y},{new:!0,runValidators:!0}).select("-emailVerificationToken -emailVerificationExpires");if(!h)return n.NextResponse.json({success:!1,error:"用户不存在"},{status:404});return n.NextResponse.json({success:!0,message:"头像上传成功",data:{avatarUrl:y,user:{id:h._id.toString(),email:h.email,name:h.name,avatar:h.avatar,role:h.role,emailVerified:h.emailVerified}}})}catch(e){return console.error("Avatar upload error:",e),n.NextResponse.json({success:!1,error:"服务器错误，请稍后重试"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/upload/avatar/route",pathname:"/api/upload/avatar",filename:"route",bundlePath:"app/api/upload/avatar/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/avatar/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:h}=g;function w(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4243,4999,580,3136],()=>t(89516));module.exports=i})();