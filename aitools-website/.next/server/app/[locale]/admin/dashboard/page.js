(()=>{var e={};e.id=4926,e.ids=[4926],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var r=t(60687),a=t(93613),l=t(11860);function o({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20701:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(60687),a=t(43210),l=t(12340),o=t(77618),n=t(78521),i=t(33823),d=t(11011),c=t(56976),x=t(48730),m=t(5336),h=t(35071),p=t(62688);let u=(0,p.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var g=t(53411);let b=(0,p.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var y=t(93613),v=t(13861);function f(){(0,l.a8)();let e=(0,o.c3)("admin"),s=(0,n.Ym)(),[t,p]=(0,a.useState)("7d"),[f,j]=(0,a.useState)(null),[N,w]=(0,a.useState)(!0),[_,A]=(0,a.useState)(""),k=async()=>{try{w(!0),A("");let s=await c.u.getAdminStats(t);s.success&&s.data?j(s.data):A(s.error||e("errors.fetch_failed"))}catch(s){A(e("errors.network_error"))}finally{w(!1)}};return N?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(i.A,{size:"lg",className:"py-20"})}):(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[_&&(0,r.jsx)(d.default,{message:_,onClose:()=>A(""),className:"mb-6"}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,r.jsx)(g.A,{className:"mr-3 h-8 w-8 text-blue-600"}),e("dashboard.title")]}),(0,r.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:e("dashboard.subtitle")})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("select",{value:t,onChange:e=>p(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"1d",children:e("dashboard.time_range.today")}),(0,r.jsx)("option",{value:"7d",children:e("dashboard.time_range.7days")}),(0,r.jsx)("option",{value:"30d",children:e("dashboard.time_range.30days")}),(0,r.jsx)("option",{value:"90d",children:e("dashboard.time_range.90days")})]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e("dashboard.stats.total_tools")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:f?.overview?.totalTools||0})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,r.jsx)(g.A,{className:"w-6 h-6 text-blue-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(b,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+12%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:e("dashboard.stats.vs_last_week")})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e("dashboard.stats.pending_review")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:f?.overview?.pendingTools||0})]}),(0,r.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-yellow-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,r.jsx)("span",{className:"text-yellow-600",children:e("dashboard.stats.needs_attention")})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e("dashboard.stats.approved_today")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:(f?.overview?.approvedTools||0).toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-green-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(b,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+8%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:e("dashboard.stats.vs_last_week")})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e("dashboard.stats.rejected_today")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:(f?.overview?.rejectedTools||0).toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-red-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(b,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+15%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:e("dashboard.stats.vs_last_week")})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("dashboard.overview.title")}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(g.A,{className:"w-5 h-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e("dashboard.stats.total_tools")})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:f?.overview?.totalTools||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-yellow-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-yellow-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e("dashboard.stats.pending_review")})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-yellow-600",children:f?.overview?.pendingTools||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e("dashboard.stats.approved_today")})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:f?.overview?.approvedTools||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-red-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 text-red-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e("dashboard.stats.rejected_today")})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-red-600",children:f?.overview?.rejectedTools||0})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("dashboard.quick_actions.title")}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{onClick:()=>window.location.href="/admin",className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(v.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e("dashboard.quick_actions.view_pending")})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(u,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e("dashboard.quick_actions.view_history")})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(g.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e("dashboard.quick_actions.export_report")})]}),(0,r.jsxs)("button",{onClick:()=>k(),className:"w-full flex items-center justify-center space-x-2 p-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,r.jsx)(b,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e("dashboard.quick_actions.refresh_data")})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("dashboard.system_info.title")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:f?e("dashboard.system_info.status_online"):e("dashboard.system_info.status_offline")}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e("dashboard.system_info.system_status")})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:t}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e("dashboard.system_info.stats_period")})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:new Date().toLocaleDateString("zh"===s?"zh-CN":"en-US")}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e("dashboard.system_info.last_updated")})]})]})]})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({size:e="md",className:s=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${s}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},48654:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx","default")},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53411:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},54211:(e,s,t)=>{Promise.resolve().then(t.bind(t,48654))},56976:(e,s,t)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(!process.env.VERCEL_URL&&!process.env.NETLIFY)return"http://localhost:3001";if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function l(){return"production"}function o(){return"development"===l()}t.d(s,{u:()=>c});let n={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:l(),isDevelopment:o(),isProduction:"production"===l(),port:process.env.PORT||"3001"};o()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",n.baseUrl),console.log("  API Base URL:",n.apiBaseUrl),console.log("  NextAuth URL:",n.nextAuthUrl),console.log("  Environment:",n.environment),console.log("  Port:",n.port));let i=a();class d{constructor(e=i){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s};console.log("API request:",{url:t,config:r});let a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,s){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(s)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}}let c=new d},62688:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var r=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),o=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),i=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:o,iconNode:c,...x},m)=>(0,r.createElement)("svg",{ref:m,...d,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:n("lucide",l),...!o&&!i(x)&&{"aria-hidden":"true"},...x},[...c.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(o)?o:[o]])),x=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...l},i)=>(0,r.createElement)(c,{ref:i,iconNode:s,className:n(`lucide-${a(o(e))}`,`lucide-${e}`,t),...l}));return t.displayName=o(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64080:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),o=t.n(l),n=t(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(s,i);let d={children:["",{children:["[locale]",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,48654)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/admin/dashboard/page",pathname:"/[locale]/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},68283:(e,s,t)=>{Promise.resolve().then(t.bind(t,20701))},79551:e=>{"use strict";e.exports=require("url")},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,4999,9658,6435,6699,8232,2585],()=>t(64080));module.exports=r})();