(()=>{var e={};e.id=245,e.ids=[245],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5148:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{"use strict";e.exports=require("util")},28448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["[locale]",{children:["submit",{children:["launch-date",{children:["[toolId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96781)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/submit/launch-date/[toolId]/page",pathname:"/[locale]/submit/launch-date/[toolId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(56037),a=r.n(s),i=r(60366);let n=new s.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:i.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({launchDate:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=a().models.Tool||a().model("Tool",n)},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51465:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},57592:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx","default")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>n,PZ:()=>l,RI:()=>d,ut:()=>o});var s=r(64348);let a=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function i(e){let t=await (0,s.A)({locale:e||"en",namespace:"categories"});return a.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function n(e){return(await i(e)).map(e=>({value:e.slug,label:e.name}))}async function l(e,t){return(await i(t)).find(t=>t.slug===e)}let o=a.map(e=>e.slug),d=a.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65414:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.bind(r,99730))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91142:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")},96781:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(37413),a=r(35426),i=r(78878),n=r(12909),l=r(75745),o=r(30762),d=r(17063),c=r(31098),u=r(51465),m=r(91142),p=r(5148),x=r(64348),h=r(56037),g=r.n(h),y=r(57592);async function f(e,t,r=!1){try{if(await (0,l.A)(),!g().Types.ObjectId.isValid(e))return null;let s=await o.A.findById(e).lean();if(!s)return null;let a=await d.A.findOne({email:t}).lean();if(!a||s.submittedBy.toString()!==a._id.toString())return null;if(r){if(!s.selectedLaunchDate||"published"===s.status)return null}else if("draft"!==s.status)return null;let i=!1,n=null;if("paid"===s.launchOption){let e=await c.A.findOne({toolId:s._id,status:"completed"}).lean();e&&(i=!0,n=e._id.toString())}return{_id:s._id.toString(),name:s.name,description:s.description,status:s.status,launchOption:s.launchOption,selectedLaunchDate:s.selectedLaunchDate,hasPaidOrder:i,orderId:n}}catch(e){return console.error("Failed to fetch tool:",e),null}}async function b({params:e,searchParams:t}){let{locale:r,toolId:l}=await e,{mode:o="create"}=await t,d=await (0,x.A)("launch"),c=await (0,x.A)("common"),h=await (0,a.getServerSession)(n.N);h?.user?.email||(0,i.V2)({href:"/",locale:r});let g="edit"===o,b=await f(l,h.user.email,g);b||(0,i.V2)({href:"/",locale:r});let v=function(){let e=new Date;return e.setMonth(e.getMonth()+1),e.toISOString().split("T")[0]}(),w=function(){let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]}(),j=b.selectedLaunchDate?new Date(b.selectedLaunchDate).toISOString().split("T")[0]:b.hasPaidOrder?w:v;return(0,s.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[g&&(0,s.jsxs)("a",{href:"/profile/submitted",className:"flex items-center text-gray-600 hover:text-gray-900 mb-4",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-1"}),c("back")]}),g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:d("edit_launch_date")}),(0,s.jsx)("p",{className:"text-gray-600",children:d("edit_launch_date_description")})]}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(m.A,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:d("tool_submitted_success")}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:d("select_launch_date_prompt")})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:b.name}),(0,s.jsx)("p",{className:"text-gray-600",children:b.description})]}),g&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full mr-2 ${"pending"===b.status?"bg-yellow-400":"approved"===b.status?"bg-green-400":"bg-gray-400"}`}),(0,s.jsx)("span",{className:"text-gray-600",children:d(`status.${b.status}`)})]}),b.hasPaidOrder&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-green-500 mr-1"}),(0,s.jsx)("span",{className:"text-green-600 font-medium",children:d("paid_plan")})]})]})}),(0,s.jsx)(y.default,{toolId:l,locale:r,currentOption:b.hasPaidOrder?"paid":b.launchOption,currentDate:j,minFreeDate:v,minPaidDate:w,hasPaidOrder:b.hasPaidOrder,orderId:b.orderId,isEditMode:g})]})}},96958:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.bind(r,57592))},99730:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(60687),a=r(43210),i=r(12340),n=r(77618),l=r(78521),o=r(40228),d=r(85778),c=r(5336),u=r(48730),m=r(94865);let p=m.vS;function x({currentOption:e="free",currentDate:t,isEditing:r=!1,onSubmit:i,isSubmitting:x,error:h,hasPaidOrder:g=!1}){let[y,f]=(0,a.useState)(g?"paid":e),[b,v]=(0,a.useState)(""),w=(0,n.c3)("launch"),j=(0,l.Ym)(),N=()=>{let e=new Date;return e.setMonth(e.getMonth()+1),e.toISOString().split("T")[0]},_=()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]},S=e=>{g||(f(e),"free"===e?v(N()):v(_()))},A=async()=>{b&&await i(y,b)};return(0,s.jsxs)("div",{className:"space-y-8",children:[!g&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:r?w("select_plan"):w("select_option")}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:p.map(e=>(0,s.jsxs)("div",{className:`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${y===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"} ${"recommended"in e&&e.recommended?"ring-2 ring-blue-200":""}`,onClick:()=>S(e.id),children:["recommended"in e&&e.recommended&&(0,s.jsx)("div",{className:"absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:w("recommended")}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:["free"===e.id?(0,s.jsx)(o.A,{className:"h-6 w-6 text-gray-600 mr-3"}):(0,s.jsx)(d.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:w(`plans.${e.id}.title`)}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:w(`plans.${e.id}.description`)})]})]}),(0,s.jsx)("div",{className:"text-right",children:(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,m.$g)(e.price,j)})})]}),(0,s.jsx)("ul",{className:"space-y-2",children:e.features.map((t,r)=>(0,s.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-green-500 mr-2 flex-shrink-0"}),w(`plans.${e.id}.features.${r}`)]},r))}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("input",{type:"radio",name:"launchOption",value:e.id,checked:y===e.id,onChange:()=>S(e.id),className:"sr-only"}),(0,s.jsx)("div",{className:`w-4 h-4 rounded-full border-2 ${y===e.id?"border-blue-500 bg-blue-500":"border-gray-300"}`,children:y===e.id&&(0,s.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})})]})]},e.id))})]}),g&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-green-500 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-green-800",children:w("priority_service_activated_title")}),(0,s.jsx)("p",{className:"text-sm text-green-600 mt-1",children:w("priority_service_activated_description")})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 mr-2"}),w("select_date")]}),(0,s.jsxs)("div",{className:"max-w-md",children:[(0,s.jsx)("input",{type:"date",value:b,onChange:e=>v(e.target.value),min:g||"paid"===y?_():N(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:g?w("paid_date_info"):"free"===y?w("free_date_info"):w("paid_date_info")})]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("button",{onClick:A,disabled:x||!b,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto",children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"paid"===y?w("processing"):w("saving")]}):(0,s.jsx)(s.Fragment,{children:g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),w("save_changes")]}):"paid"===y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),r?w("upgrade_and_pay",{price:(0,m.$g)(m.kX.PRIORITY_LAUNCH.displayPrice,j)}):w("pay_amount",{price:(0,m.$g)(m.kX.PRIORITY_LAUNCH.displayPrice,j)})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),r?w("save_changes"):w("confirm_date")]})})}),h&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-4",children:h}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mt-4",children:g?w("changes_effective"):"paid"===y?w("payment_redirect"):r?w("changes_effective"):w("review_queue")})]})]})}function h({toolId:e,locale:t,currentOption:r="free",currentDate:l,minFreeDate:o,minPaidDate:d,hasPaidOrder:c=!1,orderId:u,isEditMode:m=!1}){let p=(0,i.rd)(),h=(0,n.c3)("launch"),[g,y]=(0,a.useState)(!1),[f,b]=(0,a.useState)(""),v=async(r,s)=>{y(!0),b("");try{let a=await fetch(`/api/tools/${e}/launch-date`,{method:"POST",headers:{"Content-Type":"application/json","X-Locale":t},body:JSON.stringify({launchOption:r,selectedDate:s,hasPaidOrder:c})}),i=await a.json();i.success?"paid"===r&&i.data.paymentUrl?window.location.href=i.data.paymentUrl:p.push(`/submit/launch-date-success?toolId=${e}`):b(i.message||h("submit_failed"))}catch{b(h("network_error"))}finally{y(!1)}};return(0,s.jsx)(x,{toolId:e,currentOption:c?"paid":r,currentDate:l,isEditing:m,onSubmit:v,isSubmitting:g,error:f,hasPaidOrder:c})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,8232,3136,2585,3930],()=>r(28448));module.exports=s})();