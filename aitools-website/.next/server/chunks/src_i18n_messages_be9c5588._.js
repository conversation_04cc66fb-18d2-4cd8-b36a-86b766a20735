module.exports = {

"[project]/src/i18n/messages/en.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/src_i18n_messages_en_json_22d3aad2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/i18n/messages/en.json (json)");
    });
});
}}),
"[project]/src/i18n/messages/zh.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/src_i18n_messages_zh_json_5b9b5df1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/i18n/messages/zh.json (json)");
    });
});
}}),

};