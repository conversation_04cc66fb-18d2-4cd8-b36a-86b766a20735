"use strict";exports.id=3930,exports.ids=[3930],exports.modules={5336:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},12909:(e,t,r)=>{r.d(t,{N:()=>s});var i=r(36344),a=r(65752),n=r(13581),o=r(75745),l=r(17063);let s={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,n.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,o.A)();let t=await l.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,a]=r.split(":");if(i!==e.token||a!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,o.A)();try{let i=await l.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new l.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{r.d(t,{A:()=>l});var i=r(56037),a=r.n(i);let n=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[n],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},o.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let l=a().models.User||a().model("User",o)},26373:(e,t,r)=>{r.d(t,{A:()=>u});var i=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:n="",children:o,iconNode:c,...u},m)=>(0,i.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",n),...!o&&!s(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(o)?o:[o]])),u=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...n},s)=>(0,i.createElement)(c,{ref:s,iconNode:t,className:l(`lucide-${a(o(e))}`,`lucide-${e}`,r),...n}));return r.displayName=o(e),r}},31098:(e,t,r)=>{r.d(t,{A:()=>o});var i=r(56037),a=r.n(i);let n=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:i.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({userId:1,createdAt:-1}),n.index({toolId:1}),n.index({status:1}),n.index({paymentIntentId:1}),n.index({paymentSessionId:1}),n.index({stripePaymentIntentId:1}),n.index({stripeCustomerId:1}),n.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),n.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),n.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},n.methods.markAsFailed=function(){return this.status="failed",this.save()},n.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},n.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let o=a().models.Order||a().model("Order",n)},62688:(e,t,r)=>{r.d(t,{A:()=>u});var i=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:n="",children:o,iconNode:c,...u},m)=>(0,i.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",n),...!o&&!s(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(o)?o:[o]])),u=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...n},s)=>(0,i.createElement)(c,{ref:s,iconNode:t,className:l(`lucide-${a(o(e))}`,`lucide-${e}`,r),...n}));return r.displayName=o(e),r}},75745:(e,t,r)=>{r.d(t,{A:()=>l});var i=r(56037),a=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let l=async function(){if(o.conn)return o.conn;o.promise||(o.promise=a().connect(n,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},85778:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},94865:(e,t,r)=>{r.d(t,{$g:()=>c,Ef:()=>s,Y$:()=>l,kX:()=>i,mV:()=>d,tF:()=>u,v4:()=>o,vS:()=>a});let i={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:i.FREE_LAUNCH.description,price:i.FREE_LAUNCH.displayPrice,features:i.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:i.PRIORITY_LAUNCH.description,price:i.PRIORITY_LAUNCH.displayPrice,features:i.PRIORITY_LAUNCH.features,recommended:!0}],n={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},o=[{value:"",label:"所有价格"},{value:n.FREE.value,label:n.FREE.label},{value:n.FREEMIUM.value,label:n.FREEMIUM.label},{value:n.PAID.value,label:n.PAID.label}],l=[{value:n.FREE.value,label:n.FREE.label},{value:n.FREEMIUM.value,label:n.FREEMIUM.label},{value:n.PAID.value,label:n.PAID.label}],s=e=>{switch(e){case n.FREE.value:return n.FREE.color;case n.FREEMIUM.value:return n.FREEMIUM.color;case n.PAID.value:return n.PAID.color;default:return"bg-gray-100 text-gray-800"}},d=e=>{switch(e){case n.FREE.value:return n.FREE.label;case n.FREEMIUM.value:return n.FREEMIUM.label;case n.PAID.value:return n.PAID.label;default:return e}},c=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};