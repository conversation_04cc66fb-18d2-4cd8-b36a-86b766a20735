{"/_not-found/page": "/_not-found", "/api/admin/stats/route": "/api/admin/stats", "/api/admin/tools/[id]/reject/route": "/api/admin/tools/[id]/reject", "/api/admin/tools/route": "/api/admin/tools", "/api/admin/tools/[id]/approve/route": "/api/admin/tools/[id]/approve", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/auth/me/route": "/api/auth/me", "/api/orders/[id]/route": "/api/orders/[id]", "/api/categories/route": "/api/categories", "/api/auth/send-code/route": "/api/auth/send-code", "/api/orders/[id]/pay/route": "/api/orders/[id]/pay", "/api/stripe/webhook/route": "/api/stripe/webhook", "/api/tools/[id]/comments/route": "/api/tools/[id]/comments", "/api/tools/[id]/like/route": "/api/tools/[id]/like", "/api/test/create-payment-intent/route": "/api/test/create-payment-intent", "/api/stripe/create-payment-intent/route": "/api/stripe/create-payment-intent", "/api/tools/[id]/route": "/api/tools/[id]", "/api/tools/[id]/launch-date/route": "/api/tools/[id]/launch-date", "/api/tools/publish/route": "/api/tools/publish", "/api/tools/route": "/api/tools", "/api/tools/submit/route": "/api/tools/submit", "/api/upload/logo/route": "/api/upload/logo", "/api/user/liked-tools/route": "/api/user/liked-tools", "/api/upload/avatar/route": "/api/upload/avatar", "/api/user/tools/route": "/api/user/tools", "/favicon.ico/route": "/favicon.ico", "/sitemap.xml/route": "/sitemap.xml", "/page": "/", "/[locale]/admin/dashboard/page": "/[locale]/admin/dashboard", "/[locale]/admin/page": "/[locale]/admin", "/[locale]/categories/[slug]/page": "/[locale]/categories/[slug]", "/[locale]/dashboard/page": "/[locale]/dashboard", "/[locale]/payment/checkout/page": "/[locale]/payment/checkout", "/[locale]/profile/submitted/page": "/[locale]/profile/submitted", "/[locale]/admin/tools/[id]/page": "/[locale]/admin/tools/[id]", "/[locale]/profile/liked/page": "/[locale]/profile/liked", "/[locale]/submit/edit/[toolId]/page": "/[locale]/submit/edit/[toolId]", "/[locale]/profile/page": "/[locale]/profile", "/[locale]/submit/launch-date/[toolId]/page": "/[locale]/submit/launch-date/[toolId]", "/[locale]/settings/page": "/[locale]/settings", "/[locale]/submit/success/page": "/[locale]/submit/success", "/[locale]/submit/tool-info-success/page": "/[locale]/submit/tool-info-success", "/[locale]/submit/launch-date-success/page": "/[locale]/submit/launch-date-success", "/[locale]/submit/page": "/[locale]/submit", "/[locale]/test-auth/page": "/[locale]/test-auth", "/[locale]/search/page": "/[locale]/search", "/[locale]/test-pricing/page": "/[locale]/test-pricing", "/[locale]/test-stripe/page": "/[locale]/test-stripe", "/[locale]/categories/page": "/[locale]/categories", "/[locale]/contact/page": "/[locale]/contact", "/[locale]/tools/[id]/page": "/[locale]/tools/[id]", "/[locale]/about/page": "/[locale]/about", "/[locale]/privacy/page": "/[locale]/privacy", "/[locale]/tools/page": "/[locale]/tools", "/[locale]/terms/page": "/[locale]/terms", "/[locale]/page": "/[locale]"}